# Flowbit Project Structure

## Clean, Production-Ready Structure

```
flowbit/
├── backend/                          # Node.js API Server
│   ├── src/
│   │   ├── controllers/              # Request handlers
│   │   │   ├── adminController.ts
│   │   │   ├── authController.ts
│   │   │   ├── ticketController.ts
│   │   │   ├── userController.ts
│   │   │   ├── webhookController.ts
│   │   │   └── workflowController.ts
│   │   ├── middleware/               # Express middleware
│   │   │   ├── auth.ts
│   │   │   ├── security.ts
│   │   │   └── tenant.ts
│   │   ├── models/                   # Database models
│   │   │   ├── AuditLog.ts
│   │   │   ├── Ticket.ts
│   │   │   ├── User.ts
│   │   │   └── WorkflowExecution.ts
│   │   ├── routes/                   # API routes
│   │   │   ├── admin.ts
│   │   │   ├── auth.ts
│   │   │   ├── health.ts
│   │   │   ├── ticket.ts
│   │   │   ├── user.ts
│   │   │   ├── webhook.ts
│   │   │   └── workflow.ts
│   │   ├── services/                 # Business logic
│   │   │   ├── n8nService.ts
│   │   │   └── registryService.ts
│   │   ├── utils/                    # Utilities
│   │   │   ├── database.ts
│   │   │   ├── jwt.ts
│   │   │   └── validation.ts
│   │   ├── types/                    # TypeScript types
│   │   │   └── index.ts
│   │   └── app.ts                    # Main application
│   ├── tests/                        # Test suites
│   │   ├── integration/
│   │   │   ├── auth-integration.test.ts
│   │   │   ├── registry-integration.test.ts
│   │   │   └── workflow-integration.test.ts
│   │   ├── unit/
│   │   │   ├── n8n-service.test.ts
│   │   │   ├── registry-service.test.ts
│   │   │   └── tenant-isolation.test.ts
│   │   └── setup.ts
│   ├── jest.config.js               # Jest configuration
│   ├── jest.integration.config.js   # Integration test config
│   ├── package.json                 # Dependencies & scripts
│   └── tsconfig.json                # TypeScript config
│
├── frontend/                         # React Applications
│   └── shell/                        # Main shell application
│       ├── src/
│       │   ├── components/           # React components
│       │   │   ├── ErrorBoundary.tsx
│       │   │   ├── Header.tsx
│       │   │   ├── Layout.tsx
│       │   │   ├── LoadingSpinner.tsx
│       │   │   ├── ProtectedRoute.tsx
│       │   │   └── Sidebar.tsx
│       │   ├── hooks/                # Custom hooks
│       │   │   └── useAuth.ts
│       │   ├── services/             # API services
│       │   │   ├── api.ts
│       │   │   └── auth.ts
│       │   ├── store/                # State management
│       │   │   └── authStore.ts
│       │   ├── App.tsx               # Main app component
│       │   └── index.tsx             # Entry point
│       ├── public/                   # Static assets
│       ├── package.json              # Dependencies & scripts
│       ├── tailwind.config.js        # Tailwind CSS config
│       ├── tsconfig.json             # TypeScript config
│       └── webpack.config.js         # Webpack config
│
├── n8n-workflows/                    # Workflow Automation
│   ├── README.md                     # Workflow documentation
│   └── ticket-created-workflow.json # Sample workflow
│
├── scripts/                          # Utility Scripts
│   ├── check-system-readiness.sh    # System health check
│   └── quick-setup.sh               # Quick setup script
│
├── registry.example.json             # Example tenant config
├── registry.json                     # Tenant configuration
├── README.md                         # Project documentation
└── DEPLOYMENT_CHECKLIST.md          # Deployment guide
```

## Key Features

### Backend (Node.js + TypeScript)
- **Multi-tenant Architecture**: Complete tenant isolation
- **Authentication**: JWT-based with refresh tokens
- **Security**: Rate limiting, CORS, input validation
- **Database**: MongoDB with Mongoose ODM
- **Testing**: Jest with unit and integration tests
- **Workflows**: n8n integration for automation

### Frontend (React + TypeScript)
- **Micro-frontend Ready**: Module federation setup
- **Authentication**: JWT token management
- **Theming**: Dynamic tenant-based theming
- **State Management**: Zustand for global state
- **Styling**: Tailwind CSS for responsive design

### Workflows (n8n)
- **Automation**: Ticket management workflows
- **Integration**: Webhook-based communication
- **Monitoring**: Execution tracking and analytics

## File Count Summary

- **Backend**: 27 TypeScript files
- **Frontend**: 12 TypeScript/React files
- **Tests**: 7 test files
- **Workflows**: 1 sample workflow
- **Scripts**: 2 utility scripts
- **Config**: 8 configuration files
- **Documentation**: 3 markdown files

**Total**: ~60 essential files (down from 100+ with cleanup)

## What Was Removed

### Unnecessary Directories
- `docs/` - Empty documentation folder
- `cypress/` - E2E testing (not implemented)
- `n8n/` - Empty n8n configuration
- `nginx/` - Web server config (not needed for development)
- `ngrok/` - Tunneling config (development only)
- `frontend/admin-dashboard/` - Incomplete micro-frontend
- `frontend/support-tickets/` - Incomplete micro-frontend
- `frontend/shared/` - Incomplete shared components

### Unnecessary Files
- `docker-compose.yml` - Docker orchestration
- `backend/Dockerfile` - Docker container config
- `seed-data.js` - Database seeding script
- Various humanization scripts (completed their purpose)
- Excessive documentation files

### Build Artifacts
- All `dist/` directories
- Compiled JavaScript files
- Log files

## Benefits of Cleanup

1. **Reduced Complexity**: Easier to navigate and understand
2. **Faster Setup**: Fewer dependencies to install
3. **Clear Focus**: Only essential, working components remain
4. **Better Performance**: Smaller repository size
5. **Maintainable**: Less code to maintain and debug

## Next Steps

The project is now clean and ready for:
- Development and testing
- Production deployment
- Team collaboration
- Feature additions

All core functionality remains intact while removing unnecessary complexity.
