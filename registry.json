{"tenants": {"LogisticsCo": {"name": "Logistics Corporation", "theme": "blue", "primaryColor": "#3b82f6", "secondaryColor": "#1e40af", "logo": "https://ui-avatars.com/api/?name=LC&background=3b82f6&color=fff&size=64", "favicon": "/favicon-logistics.ico", "screens": [{"id": "support-tickets", "name": "Support Tickets", "url": "/support-tickets", "icon": "ticket", "description": "Manage customer support tickets and logistics issues", "permissions": ["User", "Admin"], "badge": {"enabled": true, "color": "red", "position": "top-right"}}, {"id": "admin-dashboard", "name": "Admin Dashboard", "url": "/admin", "icon": "dashboard", "description": "Administrative controls and system management", "permissions": ["Admin"], "badge": {"enabled": false}}, {"id": "analytics", "name": "Analytics", "url": "/analytics", "icon": "chart", "description": "View performance metrics and reports", "permissions": ["Admin"], "badge": {"enabled": false}}, {"id": "user-management", "name": "User Management", "url": "/users", "icon": "users", "description": "Manage team members and permissions", "permissions": ["Admin"], "badge": {"enabled": false}}], "features": {"realTimeNotifications": true, "auditLogging": true, "workflowAutomation": true, "customBranding": true, "apiAccess": true}, "settings": {"timezone": "UTC", "dateFormat": "MM/DD/YYYY", "currency": "USD", "language": "en"}}, "RetailGmbH": {"name": "Retail GmbH", "theme": "green", "primaryColor": "#10b981", "secondaryColor": "#059669", "logo": "https://ui-avatars.com/api/?name=RG&background=10b981&color=fff&size=64", "favicon": "/favicon-retail.ico", "screens": [{"id": "support-tickets", "name": "Customer Support", "url": "/support-tickets", "icon": "support", "description": "Handle customer inquiries and product support", "permissions": ["User", "Admin"], "badge": {"enabled": true, "color": "orange", "position": "top-right"}}, {"id": "admin-dashboard", "name": "Management", "url": "/admin", "icon": "settings", "description": "System administration and configuration", "permissions": ["Admin"], "badge": {"enabled": false}}, {"id": "inventory", "name": "Inventory", "url": "/inventory", "icon": "package", "description": "Manage product inventory and stock levels", "permissions": ["User", "Admin"], "badge": {"enabled": true, "color": "blue", "position": "top-right"}}, {"id": "orders", "name": "Orders", "url": "/orders", "icon": "shopping-cart", "description": "Process and track customer orders", "permissions": ["User", "Admin"], "badge": {"enabled": false}}], "features": {"realTimeNotifications": true, "auditLogging": true, "workflowAutomation": true, "customBranding": false, "apiAccess": false}, "settings": {"timezone": "Europe/Berlin", "dateFormat": "DD.MM.YYYY", "currency": "EUR", "language": "de"}}}, "globalSettings": {"supportedLanguages": ["en", "de", "fr", "es"], "supportedTimezones": ["UTC", "Europe/Berlin", "America/New_York", "Asia/Tokyo"], "supportedCurrencies": ["USD", "EUR", "GBP", "JPY"], "maxUsersPerTenant": 100, "maxTicketsPerTenant": 10000, "sessionTimeoutMinutes": 60, "passwordPolicy": {"minLength": 8, "requireUppercase": true, "requireLowercase": true, "requireNumbers": true, "requireSpecialChars": true}, "auditLogRetentionDays": 90, "backupRetentionDays": 30}, "version": "1.0.0", "lastUpdated": "2024-01-15T10:00:00Z"}