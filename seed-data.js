// MongoDB seed data for development
db = db.getSiblingDB('flowbit');

// Create collections
db.createCollection('users');
db.createCollection('tickets');
db.createCollection('auditlogs');

// Create indexes for performance and tenant isolation
db.users.createIndex({ "customerId": 1 });
db.users.createIndex({ "email": 1, "customerId": 1 }, { unique: true });
db.users.createIndex({ "customerId": 1, "deletedAt": 1 });

db.tickets.createIndex({ "customerId": 1 });
db.tickets.createIndex({ "customerId": 1, "userId": 1 });
db.tickets.createIndex({ "customerId": 1, "status": 1 });
db.tickets.createIndex({ "customerId": 1, "createdAt": -1 });
db.tickets.createIndex({ "customerId": 1, "deletedAt": 1 });

db.auditlogs.createIndex({ "customerId": 1 });
db.auditlogs.createIndex({ "customerId": 1, "userId": 1 });
db.auditlogs.createIndex({ "customerId": 1, "action": 1 });
db.auditlogs.createIndex({ "customerId": 1, "timestamp": -1 });

// Seed users for LogisticsCo
db.users.insertMany([
  {
    customerId: "LogisticsCo",
    email: "<EMAIL>",
    password: "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.G", // password123
    role: "Admin",
    profile: {
      firstName: "John",
      lastName: "Admin",
      avatar: "https://ui-avatars.com/api/?name=John+Admin&background=3b82f6&color=fff"
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    customerId: "LogisticsCo",
    email: "<EMAIL>",
    password: "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.G", // password123
    role: "User",
    profile: {
      firstName: "Jane",
      lastName: "User",
      avatar: "https://ui-avatars.com/api/?name=Jane+User&background=10b981&color=fff"
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// Seed users for RetailGmbH
db.users.insertMany([
  {
    customerId: "RetailGmbH",
    email: "<EMAIL>",
    password: "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.G", // password123
    role: "Admin",
    profile: {
      firstName: "Hans",
      lastName: "Mueller",
      avatar: "https://ui-avatars.com/api/?name=Hans+Mueller&background=059669&color=fff"
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    customerId: "RetailGmbH",
    email: "<EMAIL>",
    password: "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.G", // password123
    role: "User",
    profile: {
      firstName: "Anna",
      lastName: "Schmidt",
      avatar: "https://ui-avatars.com/api/?name=Anna+Schmidt&background=16a34a&color=fff"
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// Seed sample tickets for LogisticsCo
db.tickets.insertMany([
  {
    customerId: "LogisticsCo",
    userId: db.users.findOne({customerId: "LogisticsCo", role: "User"})._id,
    title: "Shipment Delay Issue",
    description: "Customer complaining about delayed shipment #LS-2024-001",
    status: "Open",
    priority: "High",
    category: "Logistics",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    customerId: "LogisticsCo",
    userId: db.users.findOne({customerId: "LogisticsCo", role: "User"})._id,
    title: "Tracking System Bug",
    description: "Tracking system showing incorrect location data",
    status: "InProgress",
    priority: "Medium",
    category: "Technical",
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// Seed sample tickets for RetailGmbH
db.tickets.insertMany([
  {
    customerId: "RetailGmbH",
    userId: db.users.findOne({customerId: "RetailGmbH", role: "User"})._id,
    title: "Product Return Request",
    description: "Customer wants to return damaged product #RT-2024-001",
    status: "Open",
    priority: "Medium",
    category: "Returns",
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

print("✅ Database seeded successfully with sample data for multi-tenant testing");
print("📊 Created users for LogisticsCo and RetailGmbH tenants");
print("🎫 Created sample tickets for both tenants");
print("🔍 Created performance indexes for tenant isolation");
print("");
print("🔐 Login credentials:");
print("LogisticsCo Admin: <EMAIL> / password123");
print("LogisticsCo User: <EMAIL> / password123");
print("RetailGmbH Admin: <EMAIL> / password123");
print("RetailGmbH User: <EMAIL> / password123");
