version: "2"
authtoken: ${NGROK_AUTHTOKEN}

tunnels:
  backend:
    addr: backend:3001
    proto: http
    hostname: flowbit-backend.ngrok.io
    bind_tls: true
    inspect: true
    
  frontend:
    addr: frontend-shell:3000
    proto: http
    hostname: flowbit-frontend.ngrok.io
    bind_tls: true
    inspect: true
    
  n8n:
    addr: n8n:5678
    proto: http
    hostname: flowbit-n8n.ngrok.io
    bind_tls: true
    inspect: true

# Global configuration
log_level: info
log_format: json
log: /var/log/ngrok.log

# Web interface configuration
web_addr: 0.0.0.0:4040
inspect_db_size: 50000000

# Update configuration
update_channel: stable
update_check: true

# Console UI configuration
console_ui: true
console_ui_color: auto
