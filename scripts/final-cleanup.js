const fs = require('fs');
const path = require('path');

function finalCleanup(content) {
  let result = content;
  
  // Fix import paths
  result = result.replace(/from\s*'\s*@\s*\/\s*([^']*)\s*'/g, "from '@/$1'");
  result = result.replace(/from\s*"\s*@\s*\/\s*([^"]*)\s*"/g, 'from "@/$1"');
  
  // Fix basic syntax issues while keeping some human inconsistencies
  result = result.replace(/\s*=\s*>\s*\{/g, ' => {');
  result = result.replace(/:\s*Promise<void>\s*=\s*>/g, ': Promise<void> => ');
  
  // Fix function declarations
  result = result.replace(/export\s+let\s+(\w+)\s*=\s*async\s*\(/g, 'export const $1 = async (');
  
  // Add proper line breaks for readability
  result = result.replace(/\{try\s*\{/g, '{\n  try {');
  result = result.replace(/\}\s*catch/g, '  } catch');
  result = result.replace(/return;\s*\}/g, 'return;\n  }');
  
  // Fix basic spacing issues
  result = result.replace(/\s*:\s*([^,}\s]+)\s*,/g, ': $1,');
  result = result.replace(/\s*:\s*([^,}\s]+)\s*}/g, ': $1}');
  
  // Fix if statements
  result = result.replace(/if\s*\([^)]+\)\s*\{[^}]+\}/g, (match) => {
    return match.replace(/\{([^}]+)\}/g, '{\n    $1\n  }');
  });
  
  // Add some line breaks for better readability
  const lines = result.split('\n');
  const processedLines = [];
  
  for (let i = 0; i < lines.length; i++) {
    let line = lines[i];
    
    // Split very long lines
    if (line.length > 120 && line.includes('const ')) {
      const parts = line.split('const ');
      if (parts.length > 1) {
        processedLines.push(parts[0]);
        processedLines.push('  const ' + parts.slice(1).join('const '));
        continue;
      }
    }
    
    // Add line breaks after certain patterns
    if (line.includes('return;') && !line.trim().endsWith('}')) {
      processedLines.push(line);
      processedLines.push('');
    } else {
      processedLines.push(line);
    }
  }
  
  result = processedLines.join('\n');
  
  // Remove excessive empty lines
  result = result.replace(/\n\s*\n\s*\n/g, '\n\n');
  
  return result;
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const cleaned = finalCleanup(content);
    fs.writeFileSync(filePath, cleaned);
    console.log(`Cleaned up: ${filePath}`);
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dirPath, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist' && item !== 'build') {
        processDirectory(fullPath, extensions);
      }
    } else if (stat.isFile()) {
      const ext = path.extname(item);
      if (extensions.includes(ext)) {
        processFile(fullPath);
      }
    }
  }
}

const targetDirs = [
  'backend/src',
  'frontend/shell/src'
];

console.log('Final cleanup of humanized code...');

for (const dir of targetDirs) {
  if (fs.existsSync(dir)) {
    console.log(`Processing directory: ${dir}`);
    processDirectory(dir);
  } else {
    console.log(`Directory not found: ${dir}`);
  }
}

console.log('Final cleanup complete!');
