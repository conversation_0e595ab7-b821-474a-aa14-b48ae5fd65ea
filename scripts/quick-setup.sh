#!/bin/bash

# Flowbit Quick Setup Script
echo "🚀 Flowbit Quick Setup"
echo "====================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if Node.js is installed
check_nodejs() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_status "Node.js found: $NODE_VERSION"
        return 0
    else
        print_error "Node.js not found. Please install Node.js 18+ first."
        return 1
    fi
}

# Check if MongoDB is available
check_mongodb() {
    if command -v mongod &> /dev/null; then
        print_status "MongoDB found"
        return 0
    elif command -v docker &> /dev/null; then
        print_info "MongoDB not found locally, but Docker is available"
        print_info "You can run: docker run -d -p 27017:27017 --name mongodb mongo:latest"
        return 0
    else
        print_warning "MongoDB not found. Please install MongoDB or Docker."
        return 1
    fi
}

# Setup backend
setup_backend() {
    print_info "Setting up backend..."
    
    cd backend || exit 1
    
    # Install dependencies
    print_info "Installing backend dependencies..."
    npm install
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
        print_info "Creating backend .env file..."
        cat > .env << EOF
NODE_ENV=development
PORT=3001
MONGODB_URI=mongodb://localhost:27017/flowbit
JWT_SECRET=$(openssl rand -base64 32)
JWT_REFRESH_SECRET=$(openssl rand -base64 32)
N8N_BASE_URL=http://localhost:5678
N8N_WEBHOOK_SECRET=n8n-webhook-secret-key
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
EOF
        print_status "Backend .env file created"
    else
        print_status "Backend .env file already exists"
    fi
    
    cd ..
}

# Setup frontend
setup_frontend() {
    print_info "Setting up frontend shell..."
    
    cd frontend/shell || exit 1
    
    # Install dependencies
    print_info "Installing frontend dependencies..."
    npm install
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
        print_info "Creating frontend .env file..."
        cat > .env << EOF
REACT_APP_API_URL=http://localhost:3001/api
REACT_APP_WS_URL=ws://localhost:3001
REACT_APP_APP_NAME=Flowbit
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development
EOF
        print_status "Frontend .env file created"
    else
        print_status "Frontend .env file already exists"
    fi
    
    cd ../..
}

# Setup registry
setup_registry() {
    print_info "Setting up tenant registry..."
    
    if [ ! -f registry.json ]; then
        cp registry.example.json registry.json
        print_status "Registry file created from example"
    else
        print_status "Registry file already exists"
    fi
}

# Create test user script
create_test_user_script() {
    print_info "Creating test user script..."
    
    cat > scripts/create-test-user.sh << 'EOF'
#!/bin/bash

echo "Creating test user..."

# Wait for backend to be ready
echo "Waiting for backend to be ready..."
while ! curl -s http://localhost:3001/api/health > /dev/null; do
    sleep 2
done

# Create admin user
echo "Creating admin user..."
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123!",
    "customerId": "demo-company",
    "role": "Admin",
    "profile": {
      "firstName": "Demo",
      "lastName": "Admin"
    }
  }'

echo ""
echo "✓ Admin user created:"
echo "  Email: <EMAIL>"
echo "  Password: Password123!"
echo "  Tenant: demo-company"

# Create regular user
echo ""
echo "Creating regular user..."
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123!",
    "customerId": "demo-company",
    "role": "User",
    "profile": {
      "firstName": "Demo",
      "lastName": "User"
    }
  }'

echo ""
echo "✓ Regular user created:"
echo "  Email: <EMAIL>"
echo "  Password: Password123!"
echo "  Tenant: demo-company"
EOF

    chmod +x scripts/create-test-user.sh
    print_status "Test user script created"
}

# Create start script
create_start_script() {
    print_info "Creating start script..."
    
    cat > scripts/start-all.sh << 'EOF'
#!/bin/bash

echo "🚀 Starting Flowbit Platform"
echo "============================"

# Function to check if port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠ Port $1 is already in use"
        return 1
    else
        return 0
    fi
}

# Check required ports
echo "Checking ports..."
check_port 3001 || exit 1
check_port 3000 || exit 1
check_port 5678 || echo "⚠ Port 5678 (n8n) is in use - this is OK if n8n is already running"

# Start MongoDB if not running
if ! pgrep mongod > /dev/null; then
    echo "Starting MongoDB..."
    mongod --fork --logpath /tmp/mongodb.log --dbpath /tmp/mongodb-data
    sleep 2
fi

# Start n8n if not running
if ! curl -s http://localhost:5678/healthz > /dev/null; then
    echo "Starting n8n..."
    npx n8n &
    sleep 5
fi

# Start backend
echo "Starting backend..."
cd backend
npm run dev &
BACKEND_PID=$!
cd ..

# Wait for backend to be ready
echo "Waiting for backend to start..."
while ! curl -s http://localhost:3001/api/health > /dev/null; do
    sleep 2
done

# Start frontend
echo "Starting frontend..."
cd frontend/shell
npm start &
FRONTEND_PID=$!
cd ../..

echo ""
echo "🎉 Flowbit Platform Started!"
echo "=========================="
echo "Frontend: http://localhost:3000"
echo "Backend:  http://localhost:3001"
echo "n8n:      http://localhost:5678"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for interrupt
trap 'echo "Stopping services..."; kill $BACKEND_PID $FRONTEND_PID; exit' INT
wait
EOF

    chmod +x scripts/start-all.sh
    print_status "Start script created"
}

# Main setup function
main() {
    echo "Starting Flowbit setup..."
    echo ""
    
    # Check prerequisites
    print_info "Checking prerequisites..."
    check_nodejs || exit 1
    check_mongodb
    
    echo ""
    
    # Create scripts directory
    mkdir -p scripts
    
    # Setup components
    setup_backend
    setup_frontend
    setup_registry
    create_test_user_script
    create_start_script
    
    echo ""
    print_status "Setup completed successfully!"
    echo ""
    echo "🎯 Next Steps:"
    echo "=============="
    echo "1. Start MongoDB (if not running):"
    echo "   mongod"
    echo ""
    echo "2. Start all services:"
    echo "   ./scripts/start-all.sh"
    echo ""
    echo "3. Create test users:"
    echo "   ./scripts/create-test-user.sh"
    echo ""
    echo "4. Open browser:"
    echo "   http://localhost:3000"
    echo ""
    echo "5. Login with:"
    echo "   Email: <EMAIL>"
    echo "   Password: Password123!"
    echo ""
    print_info "For detailed testing instructions, see TESTING_GUIDE.md"
    print_info "For deployment information, see DEPLOYMENT_CHECKLIST.md"
}

# Run main function
main
