const fs = require('fs');
const path = require('path');

function fixSyntaxErrors(content) {
  let result = content;
  
  result = result.replace(/import\s*\{\s*([^}]*)\s*\}\s*from\s*'\s*@\s*\/\s*([^']*)\s*'/g, "import { $1 } from '@/$2'");
  result = result.replace(/import\s+([^']*)\s+from\s*'\s*@\s*\/\s*([^']*)\s*'/g, "import $1 from '@/$2'");
  
  result = result.replace(/import\s*\{\s*([^}]*)\s*\}\s*from\s*"\s*@\s*\/\s*([^"]*)\s*"/g, 'import { $1 } from "@/$2"');
  result = result.replace(/import\s+([^"]*)\s+from\s*"\s*@\s*\/\s*([^"]*)\s*"/g, 'import $1 from "@/$2"');
  
  result = result.replace(/import\s*\{/g, 'import {');
  result = result.replace(/\}\s*from/g, '} from');
  
  result = result.replace(/from\s*'\s*@\s*\/\s*/g, "from '@/");
  result = result.replace(/from\s*"\s*@\s*\/\s*/g, 'from "@/');
  
  result = result.replace(/'\s*;/g, "';");
  result = result.replace(/"\s*;/g, '";');
  
  result = result.replace(/app\s*\[\s*'use'\s*\]/g, 'app.use');
  result = result.replace(/app\s*\[\s*"use"\s*\]/g, 'app.use');
  
  result = result.replace(/express\s*\[\s*'([^']*)'\s*\]/g, 'express.$1');
  result = result.replace(/express\s*\[\s*"([^"]*)"\s*\]/g, 'express.$1');
  
  result = result.replace(/(\w+)\s*\[\s*'(\w+)'\s*\]/g, '$1.$2');
  result = result.replace(/(\w+)\s*\[\s*"(\w+)"\s*\]/g, '$1.$2');
  
  result = result.replace(/import\s*\{\s*([^}]*)\s*\}\s*from\s*'([^']*)'\s*;/g, (match, imports, path) => {
    const cleanImports = imports.replace(/\s+/g, ' ').trim();
    return `import { ${cleanImports} } from '${path}';`;
  });
  
  result = result.replace(/import\s+([^']*)\s+from\s*'([^']*)'\s*;/g, (match, name, path) => {
    const cleanName = name.trim();
    return `import ${cleanName} from '${path}';`;
  });
  
  result = result.replace(/\s*:\s*([^,}\s]+)\s*,/g, ': $1,');
  result = result.replace(/\s*:\s*([^,}\s]+)\s*}/g, ': $1}');
  
  result = result.replace(/\(\s*\{/g, '({');
  result = result.replace(/\}\s*\)/g, '})');
  
  result = result.replace(/\s*=>\s*\{/g, ' => {');
  
  result = result.replace(/\s*\{\s*$/gm, ' {');
  result = result.replace(/\s*\}\s*$/gm, '}');
  
  result = result.replace(/\s*;\s*$/gm, ';');
  
  result = result.replace(/\s*,\s*$/gm, ',');
  
  result = result.replace(/if\s*\(/g, 'if (');
  result = result.replace(/for\s*\(/g, 'for (');
  result = result.replace(/while\s*\(/g, 'while (');
  result = result.replace(/catch\s*\(/g, 'catch (');
  
  result = result.replace(/\s*\+\s*/g, ' + ');
  result = result.replace(/\s*-\s*/g, ' - ');
  result = result.replace(/\s*\*\s*/g, ' * ');
  result = result.replace(/\s*\/\s*/g, ' / ');
  result = result.replace(/\s*===\s*/g, ' === ');
  result = result.replace(/\s*!==\s*/g, ' !== ');
  result = result.replace(/\s*&&\s*/g, ' && ');
  result = result.replace(/\s*\|\|\s*/g, ' || ');
  
  result = result.replace(/\s*\?\s*/g, ' ? ');
  result = result.replace(/\s*:\s*/g, ' : ');
  
  result = result.replace(/\s*=\s*/g, ' = ');
  
  result = result.replace(/\s*\.\s*/g, '.');
  
  result = result.replace(/\s*,\s*/g, ', ');
  
  result = result.replace(/\(\s*/g, '(');
  result = result.replace(/\s*\)/g, ')');
  
  result = result.replace(/\[\s*/g, '[');
  result = result.replace(/\s*\]/g, ']');
  
  result = result.replace(/\{\s*/g, '{');
  result = result.replace(/\s*\}/g, '}');
  
  result = result.replace(/\n\s*\n\s*\n/g, '\n\n');
  
  result = result.replace(/^\s*\n/, '');
  result = result.replace(/\n\s*$/, '\n');
  
  const lines = result.split('\n');
  const fixedLines = [];
  
  for (let line of lines) {
    line = line.replace(/\s+/g, ' ').trim();
    
    if (line.includes('import') && line.includes('@/')) {
      line = line.replace(/\s+/g, ' ');
    }
    
    if (line.endsWith(',') || line.endsWith(';') || line.endsWith('{') || line.endsWith('}')) {
      fixedLines.push(line);
    } else if (line.trim() === '') {
      fixedLines.push('');
    } else {
      fixedLines.push(line);
    }
  }
  
  return fixedLines.join('\n');
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fixed = fixSyntaxErrors(content);
    fs.writeFileSync(filePath, fixed);
    console.log(`Fixed syntax errors in: ${filePath}`);
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dirPath, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist' && item !== 'build') {
        processDirectory(fullPath, extensions);
      }
    } else if (stat.isFile()) {
      const ext = path.extname(item);
      if (extensions.includes(ext)) {
        processFile(fullPath);
      }
    }
  }
}

const targetDirs = [
  'backend/src',
  'frontend/shell/src'
];

console.log('Fixing syntax errors...');

for (const dir of targetDirs) {
  if (fs.existsSync(dir)) {
    console.log(`Processing directory: ${dir}`);
    processDirectory(dir);
  } else {
    console.log(`Directory not found: ${dir}`);
  }
}

console.log('Syntax errors fixed!');
