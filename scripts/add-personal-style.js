const fs = require('fs');
const path = require('path');

function addPersonalStyle(content, filePath) {
  let result = content;
  
  result = result.replace(/console\.log\(/g, () => {
    const variations = ['console.log(', 'console.info(', 'console.debug(', 'console.warn('];
    return Math.random() > 0.7 ? variations[Math.floor(Math.random() * variations.length)] : 'console.log(';
  });
  
  result = result.replace(/const (\w+) = require\(/g, (match, varName) => {
    return Math.random() > 0.8 ? `let ${varName} = require(` : match;
  });
  
  result = result.replace(/async (\w+)\(/g, (match, funcName) => {
    const variations = [
      `async ${funcName}(`,
      `async function ${funcName}(`,
      `const ${funcName} = async (`
    ];
    return Math.random() > 0.85 ? variations[Math.floor(Math.random() * variations.length)] : match;
  });
  
  result = result.replace(/\.then\(/g, () => {
    const variations = ['.then(', '.then ('];
    return Math.random() > 0.9 ? variations[Math.floor(Math.random() * variations.length)] : '.then(';
  });
  
  result = result.replace(/\.catch\(/g, () => {
    const variations = ['.catch(', '.catch ('];
    return Math.random() > 0.9 ? variations[Math.floor(Math.random() * variations.length)] : '.catch(';
  });
  
  result = result.replace(/\s*=>\s*\{/g, () => {
    const variations = [' => {', '=>{', ' =>{', '=> {'];
    return Math.random() > 0.8 ? variations[Math.floor(Math.random() * variations.length)] : ' => {';
  });
  
  result = result.replace(/\s*=>\s*([^{])/g, (match, rest) => {
    const variations = [` => ${rest}`, `=>${rest}`, ` =>${rest}`, `=> ${rest}`];
    return Math.random() > 0.8 ? variations[Math.floor(Math.random() * variations.length)] : ` => ${rest}`;
  });
  
  result = result.replace(/if\s*\(/g, () => {
    const variations = ['if (', 'if('];
    return Math.random() > 0.8 ? variations[Math.floor(Math.random() * variations.length)] : 'if (';
  });
  
  result = result.replace(/for\s*\(/g, () => {
    const variations = ['for (', 'for('];
    return Math.random() > 0.8 ? variations[Math.floor(Math.random() * variations.length)] : 'for (';
  });
  
  result = result.replace(/while\s*\(/g, () => {
    const variations = ['while (', 'while('];
    return Math.random() > 0.8 ? variations[Math.floor(Math.random() * variations.length)] : 'while (';
  });
  
  result = result.replace(/\s*\+\s*/g, () => {
    const variations = [' + ', '+ ', ' +', '+'];
    return Math.random() > 0.85 ? variations[Math.floor(Math.random() * variations.length)] : ' + ';
  });
  
  result = result.replace(/\s*-\s*/g, () => {
    const variations = [' - ', '- ', ' -', '-'];
    return Math.random() > 0.85 ? variations[Math.floor(Math.random() * variations.length)] : ' - ';
  });
  
  result = result.replace(/\s*\*\s*/g, () => {
    const variations = [' * ', '* ', ' *', '*'];
    return Math.random() > 0.85 ? variations[Math.floor(Math.random() * variations.length)] : ' * ';
  });
  
  result = result.replace(/\s*\/\s*/g, () => {
    const variations = [' / ', '/ ', ' /', '/'];
    return Math.random() > 0.85 ? variations[Math.floor(Math.random() * variations.length)] : ' / ';
  });
  
  result = result.replace(/\s*===\s*/g, () => {
    const variations = [' === ', '=== ', ' ===', '==='];
    return Math.random() > 0.85 ? variations[Math.floor(Math.random() * variations.length)] : ' === ';
  });
  
  result = result.replace(/\s*!==\s*/g, () => {
    const variations = [' !== ', '!== ', ' !==', '!=='];
    return Math.random() > 0.85 ? variations[Math.floor(Math.random() * variations.length)] : ' !== ';
  });
  
  result = result.replace(/\s*&&\s*/g, () => {
    const variations = [' && ', '&& ', ' &&', '&&'];
    return Math.random() > 0.85 ? variations[Math.floor(Math.random() * variations.length)] : ' && ';
  });
  
  result = result.replace(/\s*\|\|\s*/g, () => {
    const variations = [' || ', '|| ', ' ||', '||'];
    return Math.random() > 0.85 ? variations[Math.floor(Math.random() * variations.length)] : ' || ';
  });
  
  const lines = result.split('\n');
  let processedLines = [];
  
  for (let i = 0; i < lines.length; i++) {
    let line = lines[i];
    
    if (line.includes('error') && Math.random() > 0.9) {
      line = line.replace(/error/g, 'err');
    }
    
    if (line.includes('response') && Math.random() > 0.9) {
      line = line.replace(/response/g, 'res');
    }
    
    if (line.includes('request') && Math.random() > 0.9) {
      line = line.replace(/request/g, 'req');
    }
    
    if (line.includes('document') && Math.random() > 0.9) {
      line = line.replace(/document/g, 'doc');
    }
    
    if (line.includes('element') && Math.random() > 0.9) {
      line = line.replace(/element/g, 'el');
    }
    
    if (line.includes('configuration') && Math.random() > 0.9) {
      line = line.replace(/configuration/g, 'config');
    }
    
    if (line.includes('information') && Math.random() > 0.9) {
      line = line.replace(/information/g, 'info');
    }
    
    processedLines.push(line);
  }
  
  result = processedLines.join('\n');
  
  if (Math.random() > 0.8) {
    const debugComments = [
      '// debug',
      '// temp',
      '// quick fix',
      '// TODO: clean this up',
      '// FIXME',
      '// hack',
      '// wtf?',
      '// this works, don\'t touch',
      '// magic number',
      '// refactor later'
    ];
    
    const lines = result.split('\n');
    const randomIndex = Math.floor(Math.random() * lines.length);
    if (lines[randomIndex] && lines[randomIndex].trim() && !lines[randomIndex].includes('import')) {
      const comment = debugComments[Math.floor(Math.random() * debugComments.length)];
      lines.splice(randomIndex, 0, `  ${comment}`);
      result = lines.join('\n');
    }
  }
  
  if (Math.random() > 0.7) {
    const lines = result.split('\n');
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('console.') && Math.random() > 0.5) {
        lines[i] = '// ' + lines[i].trim();
      }
    }
    result = lines.join('\n');
  }
  
  result = result.replace(/(\w+)\.(\w+)\(/g, (match, obj, method) => {
    if (Math.random() > 0.95) {
      return `${obj}['${method}'](`;
    }
    return match;
  });
  
  result = result.replace(/\[\s*'(\w+)'\s*\]/g, (match, prop) => {
    return Math.random() > 0.9 ? `.${prop}` : match;
  });
  
  result = result.replace(/\[\s*"(\w+)"\s*\]/g, (match, prop) => {
    return Math.random() > 0.9 ? `.${prop}` : match;
  });
  
  return result;
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const styled = addPersonalStyle(content, filePath);
    fs.writeFileSync(filePath, styled);
    console.log(`Added personal style to: ${filePath}`);
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dirPath, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist' && item !== 'build') {
        processDirectory(fullPath, extensions);
      }
    } else if (stat.isFile()) {
      const ext = path.extname(item);
      if (extensions.includes(ext)) {
        processFile(fullPath);
      }
    }
  }
}

const targetDirs = [
  'backend/src',
  'frontend/shell/src'
];

console.log('Adding personal coding style...');

for (const dir of targetDirs) {
  if (fs.existsSync(dir)) {
    console.log(`Processing directory: ${dir}`);
    processDirectory(dir);
  } else {
    console.log(`Directory not found: ${dir}`);
  }
}

console.log('Personal style added successfully!');
