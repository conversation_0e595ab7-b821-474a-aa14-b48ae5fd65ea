#!/bin/bash

# Flowbit System Readiness Check Script
echo "🚀 Flowbit System Readiness Check"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
BACKEND_URL="http://localhost:3001"
FRONTEND_URL="http://localhost:3000"
N8N_URL="http://localhost:5678"

# Function to check if service is running
check_service() {
    local service_name=$1
    local url=$2
    local endpoint=$3
    
    echo -n "Checking $service_name... "
    
    if curl -s -f "$url$endpoint" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Running${NC}"
        return 0
    else
        echo -e "${RED}✗ Not running${NC}"
        return 1
    fi
}

# Function to check API endpoint
check_api_endpoint() {
    local endpoint=$1
    local expected_status=$2
    local description=$3
    
    echo -n "Testing $description... "
    
    response=$(curl -s -w "%{http_code}" -o /dev/null "$BACKEND_URL$endpoint")
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✓ $response${NC}"
        return 0
    else
        echo -e "${RED}✗ $response (expected $expected_status)${NC}"
        return 1
    fi
}

# Function to check database connection
check_database() {
    echo -n "Checking database connection... "
    
    response=$(curl -s "$BACKEND_URL/api/health/detailed" | jq -r '.data.database.status' 2>/dev/null)
    
    if [ "$response" = "healthy" ]; then
        echo -e "${GREEN}✓ Connected${NC}"
        return 0
    else
        echo -e "${RED}✗ Not connected${NC}"
        return 1
    fi
}

# Function to check environment variables
check_env_vars() {
    echo "Checking environment variables..."
    
    required_vars=(
        "MONGODB_URI"
        "JWT_SECRET"
        "JWT_REFRESH_SECRET"
        "N8N_BASE_URL"
        "N8N_WEBHOOK_SECRET"
    )
    
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -eq 0 ]; then
        echo -e "${GREEN}✓ All required environment variables set${NC}"
        return 0
    else
        echo -e "${RED}✗ Missing environment variables:${NC}"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        return 1
    fi
}

# Function to run tests
run_tests() {
    echo "Running tests..."
    
    echo -n "Backend unit tests... "
    if cd backend && npm test > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Passed${NC}"
    else
        echo -e "${RED}✗ Failed${NC}"
        return 1
    fi
    
    echo -n "Backend integration tests... "
    if npm run test:integration > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Passed${NC}"
    else
        echo -e "${RED}✗ Failed${NC}"
        return 1
    fi
    
    cd ..
    return 0
}

# Main checks
echo "1. Service Health Checks"
echo "------------------------"

services_ok=0
check_service "Backend API" "$BACKEND_URL" "/api/health" && ((services_ok++))
check_service "Frontend Shell" "$FRONTEND_URL" "/" && ((services_ok++))
check_service "n8n" "$N8N_URL" "/healthz" && ((services_ok++))

echo ""
echo "2. Database Connectivity"
echo "------------------------"
check_database
db_ok=$?

echo ""
echo "3. API Endpoints"
echo "---------------"

endpoints_ok=0
check_api_endpoint "/api/health" "200" "Health endpoint" && ((endpoints_ok++))
check_api_endpoint "/api/auth/login" "400" "Auth endpoint (expects validation error)" && ((endpoints_ok++))
check_api_endpoint "/api/workflows/available" "401" "Workflow endpoint (expects auth error)" && ((endpoints_ok++))

echo ""
echo "4. Environment Configuration"
echo "---------------------------"
check_env_vars
env_ok=$?

echo ""
echo "5. Test Suite"
echo "------------"
run_tests
tests_ok=$?

echo ""
echo "📊 SUMMARY"
echo "=========="
echo "Services running: $services_ok/3"
echo "Database: $([ $db_ok -eq 0 ] && echo "✓" || echo "✗")"
echo "API endpoints: $endpoints_ok/3"
echo "Environment: $([ $env_ok -eq 0 ] && echo "✓" || echo "✗")"
echo "Tests: $([ $tests_ok -eq 0 ] && echo "✓" || echo "✗")"

# Overall status
total_checks=$((3 + db_ok + 3 + env_ok + tests_ok))
if [ $services_ok -eq 3 ] && [ $db_ok -eq 0 ] && [ $endpoints_ok -eq 3 ] && [ $env_ok -eq 0 ] && [ $tests_ok -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 SYSTEM READY FOR PRODUCTION!${NC}"
    exit 0
else
    echo ""
    echo -e "${RED}❌ SYSTEM NOT READY - Please fix the issues above${NC}"
    exit 1
fi
