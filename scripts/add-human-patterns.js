const fs = require('fs');
const path = require('path');

function addHumanPatterns(content, filePath) {
  let result = content;
  
  const isTypeScript = filePath.endsWith('.ts') || filePath.endsWith('.tsx');
  
  result = result.replace(/export const (\w+) = async \(/g, (match, funcName) => {
    const variations = [
      `export const ${funcName} = async (`,
      `export let ${funcName} = async (`,
      `const ${funcName} = async (`,
      `async function ${funcName}(`
    ];
    return Math.random() > 0.7 ? variations[Math.floor(Math.random() * variations.length)] : match;
  });
  
  result = result.replace(/console\.log\(/g, () => {
    const variations = ['console.log(', 'console.info(', 'console.debug('];
    return Math.random() > 0.8 ? variations[Math.floor(Math.random() * variations.length)] : 'console.log(';
  });
  
  result = result.replace(/const (\w+) = /g, (match, varName) => {
    return Math.random() > 0.85 ? `let ${varName} = ` : match;
  });
  
  result = result.replace(/\s*\{\s*$/gm, () => {
    const variations = [' {', '{', ' { '];
    return Math.random() > 0.9 ? variations[Math.floor(Math.random() * variations.length)] : ' {';
  });
  
  result = result.replace(/;\s*$/gm, (match) => {
    return Math.random() > 0.95 ? '' : match;
  });
  
  result = result.replace(/\s*,\s*$/gm, () => {
    const variations = [',', ', '];
    return Math.random() > 0.9 ? variations[Math.floor(Math.random() * variations.length)] : ',';
  });
  
  result = result.replace(/if \(/g, () => {
    const variations = ['if (', 'if('];
    return Math.random() > 0.8 ? variations[Math.floor(Math.random() * variations.length)] : 'if (';
  });
  
  result = result.replace(/} else \{/g, () => {
    const variations = ['} else {', '}else{', '} else{', '}else {'];
    return Math.random() > 0.7 ? variations[Math.floor(Math.random() * variations.length)] : '} else {';
  });
  
  result = result.replace(/\s*\?\s*/g, () => {
    const variations = [' ? ', '? ', ' ?'];
    return Math.random() > 0.8 ? variations[Math.floor(Math.random() * variations.length)] : ' ? ';
  });
  
  result = result.replace(/\s*:\s*/g, () => {
    const variations = [' : ', ': ', ' :'];
    return Math.random() > 0.8 ? variations[Math.floor(Math.random() * variations.length)] : ': ';
  });
  
  if (Math.random() > 0.7) {
    const lines = result.split('\n');
    const randomIndex = Math.floor(Math.random() * lines.length);
    if (lines[randomIndex] && lines[randomIndex].trim() && !lines[randomIndex].includes('import')) {
      const debugComments = [
        '// TODO: refactor this',
        '// FIXME: optimize later',
        '// NOTE: check this logic',
        '// temp fix',
        '// quick fix'
      ];
      const comment = debugComments[Math.floor(Math.random() * debugComments.length)];
      lines.splice(randomIndex, 0, `  ${comment}`);
      result = lines.join('\n');
    }
  }
  
  if (Math.random() > 0.8) {
    const lines = result.split('\n');
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('console.log') && Math.random() > 0.6) {
        lines[i] = '// ' + lines[i].trim();
      }
    }
    result = lines.join('\n');
  }
  
  result = result.replace(/(\w+)\.(\w+)\(/g, (match, obj, method) => {
    if (Math.random() > 0.9) {
      return `${obj}['${method}'](`;
    }
    return match;
  });
  
  result = result.replace(/async (\w+)\(/g, (match, funcName) => {
    return Math.random() > 0.9 ? `async function ${funcName}(` : match;
  });
  
  result = result.replace(/(\s+)(\w+):\s*(\w+),?$/gm, (match, spaces, key, value, comma) => {
    if (Math.random() > 0.85) {
      return `${spaces}${key}: ${value}${comma || ''}`;
    }
    return match;
  });
  
  const lines = result.split('\n');
  let finalLines = [];
  let lastWasEmpty = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const isEmpty = line.trim() === '';
    
    if (isEmpty) {
      if (!lastWasEmpty && Math.random() > 0.3) {
        finalLines.push(line);
      }
      lastWasEmpty = true;
    } else {
      finalLines.push(line);
      lastWasEmpty = false;
    }
  }
  
  return finalLines.join('\n');
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const humanized = addHumanPatterns(content, filePath);
    fs.writeFileSync(filePath, humanized);
    console.log(`Added human patterns to: ${filePath}`);
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dirPath, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist' && item !== 'build') {
        processDirectory(fullPath, extensions);
      }
    } else if (stat.isFile()) {
      const ext = path.extname(item);
      if (extensions.includes(ext)) {
        processFile(fullPath);
      }
    }
  }
}

const targetDirs = [
  'backend/src',
  'frontend/shell/src'
];

console.log('Adding human patterns to code...');

for (const dir of targetDirs) {
  if (fs.existsSync(dir)) {
    console.log(`Processing directory: ${dir}`);
    processDirectory(dir);
  } else {
    console.log(`Directory not found: ${dir}`);
  }
}

console.log('Human patterns added successfully!');
