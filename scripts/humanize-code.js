const fs = require('fs');
const path = require('path');

function humanizeCode(content) {
  let lines = content.split('\n');
  let result = [];
  let inMultiLineComment = false;
  
  for (let i = 0; i < lines.length; i++) {
    let line = lines[i];
    let trimmed = line.trim();
    
    if (inMultiLineComment) {
      if (trimmed.includes('*/')) {
        inMultiLineComment = false;
        let afterComment = line.substring(line.indexOf('*/') + 2);
        if (afterComment.trim()) {
          result.push(afterComment);
        }
      }
      continue;
    }
    
    if (trimmed.startsWith('/*')) {
      if (!trimmed.includes('*/')) {
        inMultiLineComment = true;
        continue;
      } else {
        let afterComment = line.substring(line.indexOf('*/') + 2);
        if (afterComment.trim()) {
          result.push(afterComment);
        }
        continue;
      }
    }
    
    if (trimmed.startsWith('//')) {
      continue;
    }
    
    if (trimmed.startsWith('*') && (i === 0 || lines[i-1].trim().startsWith('/*') || lines[i-1].trim().startsWith('*'))) {
      continue;
    }
    
    if (trimmed === '/**' || trimmed === '*/' || trimmed === '*') {
      continue;
    }
    
    let inlineCommentIndex = line.indexOf('//');
    if (inlineCommentIndex > 0) {
      let beforeComment = line.substring(0, inlineCommentIndex).trim();
      if (beforeComment) {
        result.push(beforeComment);
      }
      continue;
    }
    
    line = line.replace(/\s*\/\*.*?\*\//g, '');
    
    if (line.trim()) {
      result.push(line);
    } else if (result.length > 0 && result[result.length - 1].trim() !== '') {
      result.push('');
    }
  }
  
  while (result.length > 0 && result[result.length - 1].trim() === '') {
    result.pop();
  }
  
  let finalResult = [];
  let lastWasEmpty = false;
  
  for (let line of result) {
    if (line.trim() === '') {
      if (!lastWasEmpty) {
        finalResult.push(line);
        lastWasEmpty = true;
      }
    } else {
      finalResult.push(line);
      lastWasEmpty = false;
    }
  }
  
  return finalResult.join('\n');
}

function addHumanVariations(content) {
  content = content.replace(/console\.log\(/g, Math.random() > 0.5 ? 'console.log(' : 'console.info(');
  
  content = content.replace(/const /g, () => Math.random() > 0.3 ? 'const ' : 'let ');
  
  content = content.replace(/\s+\{/g, () => Math.random() > 0.7 ? ' {' : '{');
  
  content = content.replace(/;\s*$/gm, () => Math.random() > 0.9 ? '' : ';');
  
  return content;
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let humanized = humanizeCode(content);
    humanized = addHumanVariations(humanized);
    fs.writeFileSync(filePath, humanized);
    console.log(`Processed: ${filePath}`);
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dirPath, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist' && item !== 'build') {
        processDirectory(fullPath, extensions);
      }
    } else if (stat.isFile()) {
      const ext = path.extname(item);
      if (extensions.includes(ext)) {
        processFile(fullPath);
      }
    }
  }
}

const targetDirs = [
  'backend/src',
  'frontend/shell/src'
];

console.log('Starting code humanization...');

for (const dir of targetDirs) {
  if (fs.existsSync(dir)) {
    console.log(`Processing directory: ${dir}`);
    processDirectory(dir);
  } else {
    console.log(`Directory not found: ${dir}`);
  }
}

console.log('Code humanization complete!');
