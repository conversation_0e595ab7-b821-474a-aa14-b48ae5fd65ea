# Flowbit Multi-Tenant Workflow System

A production-ready multi-tenant SaaS platform that integrates with n8n workflow engine, featuring enterprise-grade tenant isolation, dynamic micro-frontend architecture, and real-time workflow automation.

## 🚀 Features

### Core Features
- **🔐 Advanced Authentication & RBAC**: JWT with refresh tokens, role-based access control, rate limiting
- **🏢 Bulletproof Tenant Isolation**: MongoDB schemas with automatic tenant filtering and comprehensive testing
- **📱 Dynamic Use-Case Registry**: Tenant-specific screens and permissions with real-time configuration
- **⚡ Micro-frontend Architecture**: React shell with Webpack Module Federation for scalable UI components
- **🔄 n8n Workflow Integration**: Automated workflows with webhook callbacks and real-time updates
- **🐳 Production Containerization**: Docker Compose with all services, networking, and persistence

### Premium Features
- **📊 Advanced Audit Logging**: Comprehensive audit trail with searchable logs and admin dashboard
- **🧪 Comprehensive Testing**: Jest unit tests and Cypress E2E tests covering all scenarios
- **🚀 CI/CD Pipeline**: GitHub Actions with automated testing, building, and deployment
- **📈 Monitoring & Analytics**: Performance metrics, health checks, and error tracking

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Shell   │    │ Support Tickets │    │ Admin Dashboard │
│   (Port 3000)   │    │   (Port 3002)   │    │   (Port 3003)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                    Nginx Reverse Proxy (Port 80/443)             │
└─────────────────────────────────┼─────────────────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                    Backend API (Port 3001)                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │    Auth     │ │   Tickets   │ │    Admin    │ │  Webhooks   │ │
│  │ Middleware  │ │ Controller  │ │ Controller  │ │  Handler    │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────┼─────────────────────────────────┘
                                 │
    ┌─────────────────────────────┼─────────────────────────────────┐
    │                            │                                 │
┌───▼────┐  ┌────▼────┐  ┌───▼────┐  ┌────▼────┐  ┌────▼────┐
│MongoDB │  │  Redis  │  │   n8n  │  │PostgreSQL│  │  ngrok  │
│(27017) │  │ (6379)  │  │ (5678) │  │  (5432)  │  │ (4040)  │
└────────┘  └─────────┘  └────────┘  └─────────┘  └─────────┘
```

## 🛠️ Tech Stack

- **Backend**: Node.js/Express with TypeScript, JWT authentication, Helmet security
- **Database**: MongoDB with Mongoose ODM and tenant isolation
- **Frontend**: React 18 with TypeScript, Webpack Module Federation, Tailwind CSS
- **Workflow Engine**: n8n (Docker container)
- **Real-time**: WebSockets (Socket.io) for live updates
- **Containerization**: Docker Compose with multi-stage builds
- **Testing**: Jest unit tests, Cypress E2E tests
- **CI/CD**: GitHub Actions workflow
- **Monitoring**: Comprehensive audit logging system

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- Node.js 18+ (for local development)
- ngrok account (for webhook testing)

### 1. Clone and Setup
```bash
git clone <repository-url>
cd flowbit-challenge
cp .env.example .env
# Edit .env with your configuration
```

### 2. Start with Docker Compose
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### 3. Access the Application
- **Main Application**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **n8n Workflow Engine**: http://localhost:5678
- **ngrok Dashboard**: http://localhost:4040

### 4. Login Credentials
```
LogisticsCo:
- Admin: <EMAIL> / password123
- User: <EMAIL> / password123

RetailGmbH:
- Admin: <EMAIL> / password123
- User: <EMAIL> / password123
```

## 📁 Project Structure

```
flowbit-challenge/
├── docker-compose.yml          # Multi-service container orchestration
├── .env.example               # Environment configuration template
├── registry.json              # Tenant configuration registry
├── seed-data.js              # Database seeding script
├── backend/                   # Node.js/Express API
│   ├── src/
│   │   ├── controllers/       # Request handlers
│   │   ├── middleware/        # Auth, validation, tenant isolation
│   │   ├── models/           # MongoDB schemas
│   │   ├── routes/           # API route definitions
│   │   ├── services/         # Business logic
│   │   ├── utils/            # Helper functions
│   │   └── types/            # TypeScript definitions
│   ├── tests/                # Jest unit & integration tests
│   └── Dockerfile            # Multi-stage container build
├── frontend/                 # React micro-frontends
│   ├── shell/                # Main application shell
│   ├── support-tickets/      # Tickets micro-frontend
│   ├── admin-dashboard/      # Admin micro-frontend
│   └── shared/               # Shared components & utilities
├── n8n/                      # Workflow engine configuration
│   ├── workflows/            # Pre-built workflow templates
│   └── custom-nodes/         # Custom n8n nodes
├── nginx/                    # Reverse proxy configuration
├── cypress/                  # E2E test suite
├── .github/workflows/        # CI/CD pipeline
└── docs/                     # Documentation
```

## 🔧 Development

### Local Development Setup
```bash
# Backend development
cd backend
npm install
npm run dev

# Frontend shell development
cd frontend/shell
npm install
npm start

# Frontend micro-frontends
cd frontend/support-tickets
npm install
npm start

cd frontend/admin-dashboard
npm install
npm start
```

### Database Management
```bash
# Seed database with sample data
docker-compose exec backend npm run seed

# Run database migrations
docker-compose exec backend npm run migrate

# Access MongoDB shell
docker-compose exec mongodb mongosh -u admin -p password123 --authenticationDatabase admin
```

### Testing
```bash
# Run all tests
npm test

# Unit tests with coverage
cd backend
npm run test:coverage

# Integration tests
npm run test:integration

# E2E tests
npx cypress open
```

## 🔐 Security Features

- **JWT Authentication**: Access & refresh tokens with secure rotation
- **Rate Limiting**: Configurable limits on API endpoints
- **Tenant Isolation**: Database-level separation with automatic filtering
- **Input Validation**: Comprehensive request validation with Joi
- **Security Headers**: Helmet.js for security headers
- **Password Security**: bcrypt with configurable rounds
- **CORS Protection**: Configurable cross-origin resource sharing
- **Audit Logging**: Complete action tracking for compliance

## 🏢 Multi-Tenant Features

### Tenant Configuration
Each tenant has its own configuration in `registry.json`:
- Custom branding (colors, logos, themes)
- Screen permissions and navigation
- Feature flags and settings
- Localization preferences

### Data Isolation
- **Database Level**: All collections include `customerId` field
- **API Level**: Automatic tenant filtering in all queries
- **UI Level**: Role-based screen visibility
- **Audit Level**: Complete tenant action separation

### Tenant Management
- Dynamic tenant onboarding
- Per-tenant feature flags
- Customizable user roles and permissions
- Tenant-specific analytics and reporting

## 🔄 Workflow Integration

### n8n Integration
- **Webhook Triggers**: Automatic workflow execution on ticket creation
- **Callback System**: n8n calls back to update ticket status
- **Security**: Webhook signature verification
- **Retry Logic**: Automatic retry on failed webhook calls
- **Monitoring**: Workflow execution logging and metrics

### Workflow Templates
Pre-built workflows for common scenarios:
- Support ticket routing and assignment
- Automated notifications and escalations
- Integration with external systems
- Custom business logic execution

## 📊 Monitoring & Analytics

### Health Monitoring
- **Health Checks**: Endpoint monitoring for all services
- **Performance Metrics**: Response times and throughput
- **Error Tracking**: Comprehensive error logging and alerting
- **Resource Monitoring**: CPU, memory, and database metrics

### Audit & Compliance
- **Action Logging**: All user actions tracked with context
- **Data Retention**: Configurable log retention policies
- **Export Capabilities**: Audit log export for compliance
- **Search & Filter**: Advanced audit log querying

## 🚀 Deployment

### Production Deployment
```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy to production
docker-compose -f docker-compose.prod.yml up -d

# Scale services
docker-compose -f docker-compose.prod.yml up -d --scale backend=3
```

### Environment Configuration
- **Development**: Full debugging and hot reload
- **Staging**: Production-like with additional logging
- **Production**: Optimized builds with security hardening

### CI/CD Pipeline
GitHub Actions workflow includes:
- Code quality checks (ESLint, Prettier)
- TypeScript compilation
- Unit and integration tests
- Security scanning
- Docker image building
- Automated deployment

## 📚 API Documentation

### Authentication Endpoints
```
POST /api/auth/login          # User login
POST /api/auth/logout         # User logout
POST /api/auth/refresh        # Refresh JWT token
POST /api/auth/forgot-password # Password reset request
POST /api/auth/reset-password  # Password reset confirmation
```

### User Management
```
GET  /api/me/profile          # Get user profile
PUT  /api/me/profile          # Update user profile
GET  /api/me/screens          # Get user's available screens
GET  /api/me/audit-logs       # Get user's audit logs
```

### Ticket Management
```
GET    /api/tickets           # List tickets (tenant-filtered)
POST   /api/tickets           # Create new ticket
GET    /api/tickets/:id       # Get ticket details
PUT    /api/tickets/:id       # Update ticket
DELETE /api/tickets/:id       # Delete ticket (soft delete)
```

### Admin Endpoints
```
GET /api/admin/users          # List all tenant users
GET /api/admin/audit-logs     # View audit logs
GET /api/admin/analytics      # System analytics
```

### Webhook Endpoints
```
POST /api/webhook/ticket-done # n8n workflow callback
POST /api/webhook/n8n-status  # n8n status updates
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Write comprehensive tests
- Update documentation
- Follow conventional commit messages
- Ensure all CI checks pass

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [n8n](https://n8n.io/) for the excellent workflow automation platform
- [React](https://reactjs.org/) and [Express](https://expressjs.com/) communities
- [MongoDB](https://www.mongodb.com/) for robust multi-tenant database capabilities
- [Docker](https://www.docker.com/) for containerization excellence

---

**Built with ❤️ for the Flowbit Technical Challenge**
```