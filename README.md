# Flowbit Multi-Tenant SaaS Platform

A modern multi-tenant SaaS platform built with Node.js, React, and n8n workflow automation.

## Features

- Multi-tenant data isolation
- JWT-based authentication
- Dynamic tenant configuration
- n8n workflow integration
- React micro-frontend architecture

## Quick Start

1. **Setup Environment**
   ```bash
   # Backend setup
   cd backend
   npm install
   cp .env.example .env
   # Edit .env with your configuration
   
   # Frontend setup
   cd ../frontend/shell
   npm install
   ```

2. **Start Services**
   ```bash
   # Start MongoDB
   mongod
   
   # Start n8n (optional)
   npx n8n
   
   # Start backend
   cd backend
   npm run dev
   
   # Start frontend
   cd ../frontend/shell
   npm start
   ```

3. **Create Registry**
   ```bash
   cp registry.example.json registry.json
   # Edit registry.json with your tenant configurations
   ```

## Project Structure

```
flowbit/
├── backend/           # Node.js API server
├── frontend/shell/    # React shell application
├── n8n-workflows/     # Workflow configurations
├── scripts/           # Utility scripts
└── registry.json      # Tenant configuration
```

## API Endpoints

- `GET /api/health` - Health check
- `POST /api/auth/login` - User authentication
- `GET /api/me/profile` - User profile
- `GET /api/tickets` - Ticket management
- `GET /api/admin/*` - Admin operations
- `GET /api/workflows/*` - Workflow management

## Development

Run tests:
```bash
cd backend
npm test
```

Build for production:
```bash
cd backend
npm run build

cd ../frontend/shell
npm run build
```

## License

MIT
