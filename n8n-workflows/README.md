# n8n Workflow Integration

This directory contains n8n workflow configurations for the Flowbit multi-tenant SaaS platform. These workflows automate various business processes triggered by events in the application.

## Overview

The n8n integration provides automated workflow capabilities for:

- **Ticket Management**: Auto-assignment, escalation, notifications
- **User Management**: Welcome emails, role-based notifications
- **Reporting**: Daily/weekly reports, analytics
- **Integrations**: Slack, email, external APIs

## Workflow Files

### Core Workflows

1. **ticket-created-workflow.json**
   - Triggered when a new ticket is created
   - Handles priority-based notifications
   - Auto-assigns tickets based on category
   - Sends Slack notifications

2. **ticket-updated-workflow.json** (to be created)
   - Triggered when ticket status/priority changes
   - Handles escalation logic
   - Notifies stakeholders of updates

3. **user-created-workflow.json** (to be created)
   - Triggered when new user is registered
   - Sends welcome emails
   - Sets up user-specific configurations

4. **daily-report-workflow.json** (to be created)
   - Scheduled daily execution
   - Generates tenant-specific reports
   - Sends summary emails to admins

## Setup Instructions

### 1. n8n Installation

```bash
# Using Docker
docker run -it --rm \
  --name n8n \
  -p 5678:5678 \
  -v ~/.n8n:/home/<USER>/.n8n \
  n8nio/n8n

# Using npm
npm install n8n -g
n8n start
```

### 2. Environment Configuration

Set the following environment variables in your Flowbit backend:

```env
# n8n Configuration
N8N_BASE_URL=http://localhost:5678
N8N_API_KEY=your-n8n-api-key
N8N_WEBHOOK_SECRET=n8n-webhook-secret-key

# Workflow IDs (configured in n8n)
N8N_TICKET_CREATED_WORKFLOW=ticket-created-workflow
N8N_TICKET_UPDATED_WORKFLOW=ticket-updated-workflow
N8N_TICKET_ASSIGNED_WORKFLOW=ticket-assigned-workflow
N8N_TICKET_RESOLVED_WORKFLOW=ticket-resolved-workflow
N8N_USER_CREATED_WORKFLOW=user-created-workflow
N8N_DAILY_REPORT_WORKFLOW=daily-report-workflow
```

### 3. Import Workflows

1. Access n8n web interface at `http://localhost:5678`
2. Go to **Workflows** → **Import from File**
3. Import each `.json` file from this directory
4. Configure credentials for external services (email, Slack, etc.)
5. Activate the workflows

### 4. Configure Webhooks

Each workflow uses webhooks for triggering and callbacks:

#### Trigger Webhooks (n8n → Flowbit)
- `POST /webhook/ticket-created` - Triggers ticket created workflow
- `POST /webhook/ticket-updated` - Triggers ticket updated workflow

#### Callback Webhooks (Flowbit → n8n)
- `POST /api/webhook/workflow-completion` - Workflow completion status
- `POST /api/webhook/ticket-workflow` - Ticket-specific workflow results
- `POST /api/webhook/n8n-health` - Health status updates

## Workflow Details

### Ticket Created Workflow

1. Receives ticket data via webhook
2. Checks ticket priority
3. Sends critical alerts for high-priority tickets
4. Posts notification to Slack
5. Auto-assigns based on category
6. Calls back to Flowbit with results

```json
{
  "ticket": {
    "id": "ticket-id",
    "title": "Ticket Title",
    "description": "Description",
    "status": "Open",
    "priority": "High",
    "category": "Technical Support",
    "customerId": "tenant-id",
    "createdAt": "2024-01-01T00:00:00Z"
  },
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "User"
  },
  "metadata": {
    "triggeredAt": "2024-01-01T00:00:00Z",
    "source": "flowbit-api"
  }
}
```

## Security

### Webhook Security

All webhooks use signature verification:

```javascript
// Verify webhook signature
const signature = req.headers['x-webhook-secret'];
if (signature !== process.env.N8N_WEBHOOK_SECRET) {
  return res.status(401).json({ err: 'Invalid signature' });
}
```

### API Authentication

n8n API calls use API key authentication:

```javascript
headers: {
  'X-N8N-API-KEY': process.env.N8N_API_KEY
}
```

## Monitoring

### Workflow Execution Tracking

The system tracks all workflow executions in the database:

- Execution ID and status
- Start/end times and duration
- Input data and results
- Error messages and retry counts

### Health Checks

Regular health checks ensure n8n connectivity:

```bash
GET /api/workflows/test-connection
```

### Metrics

Key metrics tracked:
- Workflow execution success/failure rates
- Average execution duration
- Retry counts and patterns
- Most active workflows

## Troubleshooting

### Common Issues

1. **Webhook Timeouts**
   - Increase timeout in n8n settings
   - Check network connectivity
   - Verify webhook URLs

2. **Authentication Errors**
   - Verify API key configuration
   - Check webhook secret matching
   - Ensure proper headers

3. **Workflow Failures**
   - Check n8n logs for errors
   - Verify external service credentials
   - Test individual nodes

### Debugging

Enable debug logging in n8n:

```env
N8N_LOG_LEVEL=debug
N8N_LOG_OUTPUT=console
```

View execution logs in Flowbit:

```bash
GET /api/workflows/executions?status=error
```

## Best Practices

### Workflow Design

1. **Error Handling**: Always include error handling nodes
2. **Timeouts**: Set appropriate timeouts for external calls
3. **Retries**: Configure retry logic for transient failures
4. **Logging**: Include logging for debugging

### Performance

1. **Async Processing**: Use webhooks for non-blocking operations
2. **Batching**: Group similar operations when possible
3. **Caching**: Cache frequently accessed data
4. **Monitoring**: Track execution times and optimize slow workflows

### Security

1. **Secrets Management**: Store sensitive data in n8n credentials
2. **Input Validation**: Validate all incoming webhook data
3. **Rate Limiting**: Implement rate limiting for webhook endpoints
4. **Audit Logging**: Log all workflow executions for compliance

## Development

### Testing Workflows

1. Use n8n's test execution feature
2. Create test data in development environment
3. Monitor execution logs and results
4. Validate callback data in Flowbit

### Deployment

1. Export workflows from development n8n
2. Import to production n8n
3. Update environment variables
4. Test end-to-end functionality
5. Monitor initial executions

## Support

For issues with n8n workflows:

1. Check n8n documentation: https://docs.n8n.io/
2. Review Flowbit workflow logs
3. Test individual workflow nodes
4. Contact development team for custom workflow needs
