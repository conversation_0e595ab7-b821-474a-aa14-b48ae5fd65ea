{
  "name": "Ticket Created Workflow",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "ticket-created",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-trigger",
      "name": "Webhook Trigger",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300],
      "webhookId": "ticket-created-webhook"
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{$json.ticket.priority}}",
              "operation": "equal",
              "value2": "Critical"
            }
          ]
        }
      },
      "id": "check-priority",
      "name": "Check Priority",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [460, 300]
    },
    {
      "parameters": {
        "to": "<EMAIL>",
        "subject": "🚨 Critical Ticket Created: {{$json.ticket.title}}",
        "text": "A critical priority ticket has been created:\n\nTicket ID: {{$json.ticket.id}}\nTitle: {{$json.ticket.title}}\nDescription: {{$json.ticket.description}}\nCreated by: {{$json.user.name}} ({{$json.user.email}})\nCustomer: {{$json.ticket.customerId}}\n\nPlease review immediately.\n\nView ticket: https://app.flowbit.com/tickets/{{$json.ticket.id}}",
        "options": {}
      },
      "id": "send-critical-alert",
      "name": "Send Critical Alert",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 2,
      "position": [680, 200]
    },
    {
      "parameters": {
        "url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK",
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "{\n  \"text\": \"🎫 New Ticket Created\",\n  \"blocks\": [\n    {\n      \"type\": \"section\",\n      \"text\": {\n        \"type\": \"mrkdwn\",\n        \"text\": \"*New Ticket Created*\\n\\n*Title:* {{$json.ticket.title}}\\n*Priority:* {{$json.ticket.priority}}\\n*Category:* {{$json.ticket.category}}\\n*Created by:* {{$json.user.name}}\"\n      }\n    },\n    {\n      \"type\": \"actions\",\n      \"elements\": [\n        {\n          \"type\": \"button\",\n          \"text\": {\n            \"type\": \"plain_text\",\n            \"text\": \"View Ticket\"\n          },\n          \"url\": \"https://app.flowbit.com/tickets/{{$json.ticket.id}}\",\n          \"action_id\": \"view_ticket\"\n        }\n      ]\n    }\n  ]\n}",
        "options": {}
      },
      "id": "notify-slack",
      "name": "Notify Slack",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [680, 400]
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{$json.ticket.category}}",
              "operation": "equal",
              "value2": "Technical Support"
            }
          ]
        }
      },
      "id": "check-category",
      "name": "Check Category",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [900, 300]
    },
    {
      "parameters": {
        "url": "http://localhost:3001/api/tickets/{{$json.ticket.id}}",
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "{\n  \"assignedTo\": \"tech-support-team-id\",\n  \"tags\": [\"auto-assigned\", \"technical\"]\n}",
        "options": {
          "headers": {
            "Authorization": "Bearer {{$json.metadata.apiToken}}",
            "Content-Type": "application/json"
          }
        }
      },
      "id": "auto-assign-tech",
      "name": "Auto-assign to Tech Team",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [1120, 200]
    },
    {
      "parameters": {
        "url": "http://localhost:3001/api/webhook/ticket-workflow",
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "{\n  \"executionId\": \"{{$workflow.id}}-{{$workflow.executionId}}\",\n  \"ticketId\": \"{{$json.ticket.id}}\",\n  \"action\": \"auto-assign\",\n  \"result\": {\n    \"assignedTo\": \"tech-support-team-id\",\n    \"reason\": \"Technical support category detected\"\n  },\n  \"customerId\": \"{{$json.ticket.customerId}}\"\n}",
        "options": {
          "headers": {
            "X-Webhook-Secret": "n8n-webhook-secret-key",
            "Content-Type": "application/json"
          }
        }
      },
      "id": "callback-assignment",
      "name": "Callback Assignment",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [1340, 200]
    },
    {
      "parameters": {
        "url": "http://localhost:3001/api/webhook/workflow-completion",
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "{\n  \"executionId\": \"{{$workflow.id}}-{{$workflow.executionId}}\",\n  \"workflowId\": \"{{$workflow.id}}\",\n  \"status\": \"success\",\n  \"result\": {\n    \"processed\": true,\n    \"notifications_sent\": true,\n    \"auto_assigned\": {{$json.auto_assigned || false}}\n  },\n  \"customerId\": \"{{$json.ticket.customerId}}\"\n}",
        "options": {
          "headers": {
            "X-Webhook-Secret": "n8n-webhook-secret-key",\n            \"Content-Type\": \"application/json\"\n          }\n        }\n      },\n      \"id\": \"workflow-completion\",\n      \"name\": \"Workflow Completion\",\n      \"type\": \"n8n-nodes-base.httpRequest\",\n      \"typeVersion\": 4.1,\n      \"position\": [1560, 300]\n    },\n    {\n      \"parameters\": {\n        \"respondWith\": \"json\",\n        \"responseBody\": \"{\\n  \\\"success\\\": true,\\n  \\\"message\\\": \\\"Ticket created workflow completed\\\",\\n  \\\"executionId\\\": \\\"{{$workflow.id}}-{{$workflow.executionId}}\\\",\\n  \\\"timestamp\\\": \\\"{{$now}}\\\"\n}\"\n      },\n      \"id\": \"webhook-response\",\n      \"name\": \"Webhook Response\",\n      \"type\": \"n8n-nodes-base.respondToWebhook\",\n      \"typeVersion\": 1,\n      \"position\": [1780, 300]\n    }\n  ],\n  \"connections\": {\n    \"Webhook Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Check Priority\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Check Priority\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Send Critical Alert\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ],\n        [\n          {\n            \"node\": \"Notify Slack\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Send Critical Alert\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Check Category\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Notify Slack\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Check Category\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Check Category\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Auto-assign to Tech Team\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ],\n        [\n          {\n            \"node\": \"Workflow Completion\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Auto-assign to Tech Team\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Callback Assignment\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Callback Assignment\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Workflow Completion\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Workflow Completion\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Webhook Response\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": true,\n  \"settings\": {\n    \"timezone\": \"UTC\",\n    \"saveManualExecutions\": true,\n    \"callerPolicy\": \"workflowsFromSameOwner\"\n  },\n  \"versionId\": \"1\",\n  \"meta\": {\n    \"templateCredsSetupCompleted\": true\n  },\n  \"id\": \"ticket-created-workflow\",\n  \"tags\": [\n    {\n      \"createdAt\": \"2024-01-01T00:00:00.000Z\",\n      \"updatedAt\": \"2024-01-01T00:00:00.000Z\",\n      \"id\": \"flowbit\",\n      \"name\": \"Flowbit\"\n    }\n  ]\n}
