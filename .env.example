# Database Configuration
MONGODB_URI=********************************************************************
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# API Configuration
NODE_ENV=development
PORT=3001
CORS_ORIGIN=http://localhost:3000

# n8n Configuration
N8N_WEBHOOK_SECRET=n8n-webhook-secret-key
N8N_BASE_URL=http://localhost:5678
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=password123

# Email Configuration (Mock for development)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
LOGIN_RATE_LIMIT_MAX=5

# Webhook Configuration
WEBHOOK_TIMEOUT_MS=30000
WEBHOOK_RETRY_ATTEMPTS=3
WEBHOOK_RETRY_DELAY_MS=5000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
AUDIT_LOG_RETENTION_DAYS=90

# Frontend Configuration
REACT_APP_API_URL=http://localhost:3001/api
REACT_APP_WS_URL=ws://localhost:3001
REACT_APP_APP_NAME=Flowbit Multi-Tenant Platform

# ngrok Configuration (for webhook testing)
NGROK_AUTHTOKEN=your-ngrok-auth-token

# Health Check Configuration
HEALTH_CHECK_INTERVAL_MS=30000
HEALTH_CHECK_TIMEOUT_MS=5000

# Performance Configuration
MAX_REQUEST_SIZE=10mb
REQUEST_TIMEOUT_MS=30000
DB_CONNECTION_POOL_SIZE=10

# Feature Flags
ENABLE_AUDIT_LOGGING=true
ENABLE_RATE_LIMITING=true
ENABLE_CORS=true
ENABLE_HELMET_SECURITY=true
ENABLE_REQUEST_LOGGING=true
