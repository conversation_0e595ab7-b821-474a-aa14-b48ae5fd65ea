# 🧪 Flowbit System Testing Guide

This guide provides step-by-step instructions to verify that all components of the Flowbit multi-tenant SaaS platform are working correctly.

## Prerequisites

Before testing, ensure you have:

- Node.js 18+ installed
- MongoDB running (local or cloud)
- n8n instance running
- All environment variables configured

## 🚀 Quick Start Testing

### 1. Run the Readiness Check Script

```bash
chmod +x scripts/check-system-readiness.sh
./scripts/check-system-readiness.sh
```

This script will verify:
- ✅ All services are running
- ✅ Database connectivity
- ✅ API endpoints responding
- ✅ Environment variables set
- ✅ Test suites passing

## 📋 Manual Testing Checklist

### **Phase 1: Backend API Testing**

#### 1.1 Health Checks
```bash
# Basic health check
curl http://localhost:3001/api/health

# Detailed health check
curl http://localhost:3001/api/health/detailed

# Expected: 200 status with health information
```

#### 1.2 Authentication System
```bash
# Test login (should fail with validation error)
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{}'

# Expected: 400 status with validation errors
```

#### 1.3 Database Connection
```bash
# Check database stats
curl http://localhost:3001/api/health/detailed | jq '.data.database'

# Expected: "status": "healthy"
```

### **Phase 2: Frontend Shell Testing**

#### 2.1 Application Loading
1. Open browser to `http://localhost:3000`
2. Verify loading screen appears
3. Check for JavaScript errors in console
4. Verify app loads without crashes

#### 2.2 Login Flow
1. Navigate to login page
2. Try invalid credentials (should show error)
3. Check form validation
4. Verify error messages display correctly

### **Phase 3: n8n Integration Testing**

#### 3.1 n8n Connectivity
```bash
# Test n8n health
curl http://localhost:5678/healthz

# Test n8n API (if API key configured)
curl -H "X-N8N-API-KEY: your-api-key" \
  http://localhost:5678/api/v1/workflows
```

#### 3.2 Workflow Integration
```bash
# Test workflow connection from backend
curl -H "Authorization: Bearer admin-token" \
  http://localhost:3001/api/workflows/test-connection

# Expected: {"success": true, "data": {"connected": true}}
```

### **Phase 4: End-to-End Testing**

#### 4.1 User Registration & Login
1. Create a test tenant in registry.json
2. Register a new user
3. Login with credentials
4. Verify dashboard loads with tenant branding

#### 4.2 Ticket Workflow Testing
1. Create a new ticket
2. Check n8n logs for workflow trigger
3. Verify workflow execution in database
4. Test ticket updates and assignments

#### 4.3 Multi-tenant Isolation
1. Create users in different tenants
2. Verify data isolation between tenants
3. Test cross-tenant access prevention

## 🔧 Automated Testing

### Backend Tests
```bash
cd backend

# Run unit tests
npm test

# Run integration tests
npm run test:integration

# Run tests with coverage
npm run test:coverage

# Run specific test suites
npm test -- --testNamePattern="Auth"
npm test -- --testNamePattern="Tenant"
npm test -- --testNamePattern="Workflow"
```

### Frontend Tests
```bash
cd frontend/shell

# Run unit tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 🐛 Common Issues & Solutions

### Issue 1: Database Connection Failed
**Symptoms**: Health check shows database unhealthy
**Solutions**:
```bash
# Check MongoDB is running
mongosh --eval "db.adminCommand('ping')"

# Verify connection string
echo $MONGODB_URI

# Check network connectivity
telnet localhost 27017
```

### Issue 2: n8n Not Responding
**Symptoms**: Workflow tests fail, n8n health check fails
**Solutions**:
```bash
# Check n8n is running
curl http://localhost:5678/healthz

# Restart n8n
docker restart n8n
# or
n8n start

# Check n8n logs
docker logs n8n
```

### Issue 3: Frontend Won't Load
**Symptoms**: Blank page, console errors
**Solutions**:
```bash
# Check if backend is running
curl http://localhost:3001/api/health

# Rebuild frontend
cd frontend/shell
npm run build
npm start

# Check for port conflicts
lsof -i :3000
```

### Issue 4: Authentication Errors
**Symptoms**: Login fails, token errors
**Solutions**:
```bash
# Verify JWT secrets are set
echo $JWT_SECRET
echo $JWT_REFRESH_SECRET

# Check token expiration
# Tokens expire after 15 minutes by default

# Clear browser storage
# Application → Storage → Clear storage
```

## 📊 Performance Testing

### Load Testing with Artillery
```bash
# Install artillery
npm install -g artillery

# Create test config
cat > load-test.yml << EOF
config:
  target: 'http://localhost:3001'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: "Health check"
    requests:
      - get:
          url: "/api/health"
EOF

# Run load test
artillery run load-test.yml
```

### Database Performance
```bash
# Check database performance
curl http://localhost:3001/api/health/detailed | jq '.data.database.stats'

# Monitor query performance
# Use MongoDB Compass or mongosh to analyze slow queries
```

## 🔒 Security Testing

### Authentication Security
```bash
# Test JWT token validation
curl -H "Authorization: Bearer invalid-token" \
  http://localhost:3001/api/me/profile

# Test rate limiting
for i in {1..20}; do
  curl -X POST http://localhost:3001/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"email":"test","password":"test"}'
done
```

### Tenant Isolation
```bash
# Test cross-tenant data access
# 1. Login as user from tenant A
# 2. Try to access data from tenant B
# 3. Verify access is denied
```

## 📈 Monitoring & Observability

### Application Metrics
```bash
# Check application health
curl http://localhost:3001/api/health/detailed

# Monitor workflow executions
curl -H "Authorization: Bearer admin-token" \
  http://localhost:3001/api/workflows/stats
```

### Log Analysis
```bash
# Backend logs
tail -f backend/logs/app.log

# n8n logs
docker logs -f n8n

# Database logs
tail -f /var/log/mongodb/mongod.log
```

## ✅ Production Readiness Checklist

Before deploying to production, verify:

### Infrastructure
- [ ] Database backups configured
- [ ] SSL certificates installed
- [ ] Load balancer configured
- [ ] Monitoring alerts set up
- [ ] Log aggregation configured

### Security
- [ ] All secrets properly configured
- [ ] Rate limiting enabled
- [ ] CORS properly configured
- [ ] Security headers implemented
- [ ] Vulnerability scan completed

### Performance
- [ ] Load testing completed
- [ ] Database indexes optimized
- [ ] CDN configured for static assets
- [ ] Caching strategies implemented
- [ ] Performance monitoring enabled

### Functionality
- [ ] All test suites passing
- [ ] End-to-end workflows tested
- [ ] Multi-tenant isolation verified
- [ ] Backup/restore procedures tested
- [ ] Disaster recovery plan tested

## 🆘 Getting Help

If you encounter issues:

1. **Check the logs** first (backend, n8n, database)
2. **Run the readiness script** to identify specific problems
3. **Review the troubleshooting section** in each component's README
4. **Check environment variables** are correctly set
5. **Verify all services are running** and accessible

### Debug Mode
Enable debug logging:
```bash
# Backend debug mode
NODE_ENV=development DEBUG=* npm start

# n8n debug mode
N8N_LOG_LEVEL=debug n8n start
```

### Health Check Endpoints
- Backend: `http://localhost:3001/api/health`
- Frontend: `http://localhost:3000` (should load without errors)
- n8n: `http://localhost:5678/healthz`
- Database: Check via backend health endpoint

Remember: A fully functional system should pass all automated tests and manual verification steps outlined in this guide.
