// User roles enum
export enum UserRole {
  SUPER_ADMIN = 'SuperAdmin',
  ADMIN = 'Admin',
  USER = 'User'
}

// Ticket status enum
export enum TicketStatus {
  OPEN = 'Open',
  IN_PROGRESS = 'InProgress',
  RESOLVED = 'Resolved',
  CLOSED = 'Closed'
}

// Ticket priority enum
export enum TicketPriority {
  LOW = 'Low',
  MEDIUM = 'Medium',
  HIGH = 'High',
  CRITICAL = 'Critical'
}

// User profile interface
export interface UserProfile {
  firstName: string;
  lastName: string;
  avatar?: string;
}

// User interface
export interface User {
  id: string;
  email: string;
  role: UserRole;
  profile: UserProfile;
  customerId: string;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

// Screen configuration interface
export interface ScreenConfig {
  id: string;
  name: string;
  url: string;
  icon: string;
  description?: string;
  permissions: UserRole[];
  badge?: {
    enabled: boolean;
    color?: string;
    position?: string;
    count?: number;
  };
}

// Tenant configuration interface
export interface TenantConfig {
  id: string;
  name: string;
  theme: string;
  primaryColor: string;
  secondaryColor: string;
  logo: string;
  favicon?: string;
  screens: ScreenConfig[];
  features: Record<string, boolean>;
  settings: Record<string, any>;
}

// Authentication tokens
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

// Login response
export interface LoginResponse {
  user: User;
  tokens: AuthTokens;
}

// API response interface
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Array<{
    field: string;
    message: string;
  }>;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Ticket interface
export interface Ticket {
  _id: string;
  customerId: string;
  userId: string | User;
  title: string;
  description: string;
  status: TicketStatus;
  priority: TicketPriority;
  category: string;
  workflowId?: string;
  assignedTo?: string | User;
  tags: string[];
  attachments: string[];
  comments: TicketComment[];
  createdAt: string;
  updatedAt: string;
  ageInDays?: number;
  commentCount?: number;
}

// Ticket comment interface
export interface TicketComment {
  _id: string;
  userId: string | User;
  content: string;
  createdAt: string;
}

// Audit log interface
export interface AuditLog {
  _id: string;
  customerId: string;
  userId: string | User;
  action: string;
  resourceType: string;
  resourceId?: string;
  details: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
}

// Notification interface
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

// Theme interface
export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent?: string;
  };
  mode: 'light' | 'dark';
}

// Navigation item interface
export interface NavigationItem {
  id: string;
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  badge?: {
    count: number;
    color: string;
  };
  children?: NavigationItem[];
}

// Modal props interface
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  closeOnOverlayClick?: boolean;
}

// Form field interface
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'textarea' | 'select' | 'checkbox' | 'radio';
  placeholder?: string;
  required?: boolean;
  validation?: {
    pattern?: RegExp;
    minLength?: number;
    maxLength?: number;
    custom?: (value: any) => string | undefined;
  };
  options?: Array<{
    value: string;
    label: string;
  }>;
}

// Table column interface
export interface TableColumn<T = any> {
  key: keyof T | string;
  title: string;
  sortable?: boolean;
  render?: (value: any, record: T) => React.ReactNode;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

// Pagination interface
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

// Filter interface
export interface Filter {
  key: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'daterange';
  options?: Array<{
    value: string;
    label: string;
  }>;
  value?: any;
}

// Socket event interface
export interface SocketEvent {
  type: string;
  payload: any;
  timestamp: string;
}

// Error boundary state
export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

// Loading state
export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

// Feature flag interface
export interface FeatureFlag {
  key: string;
  enabled: boolean;
  description?: string;
}

// Analytics data interface
export interface AnalyticsData {
  overview: {
    users: number;
    tickets: number;
    auditLogs: number;
  };
  tickets: {
    byStatus: Record<string, number>;
    total: number;
  };
  users: {
    byRole: Record<string, number>;
    total: number;
  };
  recentActivity: AuditLog[];
  userActivity: Array<{
    userId: string;
    actionCount: number;
    lastActivity: string;
    actions: string[];
    userEmail: string;
    userProfile: UserProfile;
  }>;
}

// Micro-frontend props
export interface MicroFrontendProps {
  user: User;
  tenant: TenantConfig;
  onNavigate?: (path: string) => void;
  onNotification?: (notification: Notification) => void;
}

// Route configuration
export interface RouteConfig {
  path: string;
  component: React.ComponentType<any>;
  exact?: boolean;
  protected?: boolean;
  roles?: UserRole[];
  title?: string;
}

// Environment configuration
export interface EnvironmentConfig {
  apiUrl: string;
  wsUrl: string;
  appName: string;
  version: string;
  environment: 'development' | 'staging' | 'production';
}
