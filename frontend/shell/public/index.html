<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Flowbit Multi-Tenant Workflow Platform" />
    
    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Inter font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Favicon and app icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://flowbit.com/">
    <meta property="og:title" content="Flowbit - Multi-Tenant Workflow Platform">
    <meta property="og:description" content="Enterprise-grade multi-tenant SaaS platform with workflow automation">
    <meta property="og:image" content="/og-image.png">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://flowbit.com/">
    <meta property="twitter:title" content="Flowbit - Multi-Tenant Workflow Platform">
    <meta property="twitter:description" content="Enterprise-grade multi-tenant SaaS platform with workflow automation">
    <meta property="twitter:image" content="/twitter-image.png">
    
    <title>Flowbit - Multi-Tenant Platform</title>
    
    <!-- CSS Variables for dynamic theming -->
    <style>
        :root {
            /* Default blue theme */
            --color-primary-50: #eff6ff;
            --color-primary-100: #dbeafe;
            --color-primary-200: #bfdbfe;
            --color-primary-300: #93c5fd;
            --color-primary-400: #60a5fa;
            --color-primary-500: #3b82f6;
            --color-primary-600: #2563eb;
            --color-primary-700: #1d4ed8;
            --color-primary-800: #1e40af;
            --color-primary-900: #1e3a8a;
            
            --color-secondary-50: #f8fafc;
            --color-secondary-100: #f1f5f9;
            --color-secondary-200: #e2e8f0;
            --color-secondary-300: #cbd5e1;
            --color-secondary-400: #94a3b8;
            --color-secondary-500: #64748b;
            --color-secondary-600: #475569;
            --color-secondary-700: #334155;
            --color-secondary-800: #1e293b;
            --color-secondary-900: #0f172a;
        }
        
        /* Loading spinner */
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--color-primary-500);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Initial loading screen */
        .initial-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--color-primary-500), var(--color-secondary-600));
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            color: white;
        }
        
        .initial-loading.fade-out {
            opacity: 0;
            transition: opacity 0.5s ease-out;
        }
    </style>
</head>
<body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    
    <!-- Initial loading screen -->
    <div id="initial-loading" class="initial-loading">
        <div class="mb-8">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </div>
        <h1 class="text-2xl font-bold mb-4">Flowbit</h1>
        <div class="loading-spinner"></div>
        <p class="mt-4 text-sm opacity-75">Loading your workspace...</p>
    </div>
    
    <!-- React app root -->
    <div id="root"></div>
    
    <!-- Error boundary fallback -->
    <div id="error-fallback" style="display: none;" class="min-h-screen flex items-center justify-center bg-gray-50">
        <div class="text-center">
            <div class="mb-4">
                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>
            <h1 class="text-xl font-semibold text-gray-900 mb-2">Something went wrong</h1>
            <p class="text-gray-600 mb-4">We're sorry, but something unexpected happened.</p>
            <button onclick="window.location.reload()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                Reload Page
            </button>
        </div>
    </div>
    
    <script>
        // Hide initial loading screen when React app loads
        window.hideInitialLoading = function() {
            const loadingElement = document.getElementById('initial-loading');
            if (loadingElement) {
                loadingElement.classList.add('fade-out');
                setTimeout(() => {
                    loadingElement.style.display = 'none';
                }, 500);
            }
        };
        
        // Show error fallback if React fails to load
        window.showErrorFallback = function() {
            document.getElementById('initial-loading').style.display = 'none';
            document.getElementById('error-fallback').style.display = 'flex';
        };
        
        // Global error handler
        window.addEventListener('error', function(event) {
            console.error('Global error:', event.error);
            // Don't show error fallback for minor errors
            if (event.error && event.error.message && event.error.message.includes('Loading chunk')) {
                return;
            }
        });
        
        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
        });
    </script>
</body>
</html>
