const HtmlWebpackPlugin = require('html-webpack-plugin');
const { ModuleFederationPlugin } = require('@module-federation/webpack');
const path = require('path');

const isDevelopment = process.env.NODE_ENV !== 'production';

module.exports = {
  mode: isDevelopment ? 'development' : 'production',
  entry: './src/index.tsx',
  
  devServer: {
    port: 3000,
    historyApiFallback: true,
    hot: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
  },

  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.jsx'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@shared': path.resolve(__dirname, '../shared'),
    },
  },

  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        use: [
          'style-loader',
          'css-loader',
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: [
                  require('tailwindcss'),
                  require('autoprefixer'),
                ],
              },
            },
          },
        ],
      },
      {
        test: /\.(png|jpe?g|gif|svg)$/i,
        type: 'asset/resource',
      },
    ],
  },

  plugins: [
    new ModuleFederationPlugin({
      name: 'shell',
      remotes: {
        supportTickets: 'supportTickets@http://localhost:3002/remoteEntry.js',
        adminDashboard: 'adminDashboard@http://localhost:3003/remoteEntry.js',
      },
      shared: {
        react: {
          singleton: true,
          requiredVersion: '^18.2.0',
        },
        'react-dom': {
          singleton: true,
          requiredVersion: '^18.2.0',
        },
        'react-router-dom': {
          singleton: true,
          requiredVersion: '^6.8.1',
        },
        'react-query': {
          singleton: true,
          requiredVersion: '^3.39.3',
        },
        axios: {
          singleton: true,
          requiredVersion: '^1.6.2',
        },
        'socket.io-client': {
          singleton: true,
          requiredVersion: '^4.7.4',
        },
        '@heroicons/react': {
          singleton: true,
          requiredVersion: '^2.0.18',
        },
        clsx: {
          singleton: true,
          requiredVersion: '^2.0.0',
        },
        'react-hot-toast': {
          singleton: true,
          requiredVersion: '^2.4.1',
        },
        zustand: {
          singleton: true,
          requiredVersion: '^4.4.7',
        },
      },
    }),
    
    new HtmlWebpackPlugin({
      template: './public/index.html',
      favicon: './public/favicon.ico',
    }),
  ],

  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    },
  },

  devtool: isDevelopment ? 'eval-source-map' : 'source-map',
};
