{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["*"], "@shared/*": ["../shared/*"]}}, "include": ["src/**/*", "../shared/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}