{"name": "flowbit-shell", "version": "1.0.0", "description": "Flowbit multi-tenant shell application", "main": "src/index.tsx", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production", "build:dev": "webpack --mode development", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.{ts,tsx}", "lint:fix": "eslint src/**/*.{ts,tsx} --fix", "format": "prettier --write src/**/*.{ts,tsx}", "typecheck": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-query": "^3.39.3", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "react-hot-toast": "^2.4.1", "react-hook-form": "^7.48.2", "react-loading-skeleton": "^3.3.1", "date-fns": "^2.30.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/node": "^20.10.5", "@types/jest": "^29.5.8", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "typescript": "^5.3.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "html-webpack-plugin": "^5.6.0", "ts-loader": "^9.5.1", "css-loader": "^6.8.1", "style-loader": "^3.3.3", "postcss": "^8.4.32", "postcss-loader": "^7.3.3", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "@webpack/module-federation": "^0.0.0", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-jest": "^29.1.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}