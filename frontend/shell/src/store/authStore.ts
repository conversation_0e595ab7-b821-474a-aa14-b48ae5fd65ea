import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { User, AuthTokens, TenantConfig } from '@shared/types';

interface AuthState {
  // State
  user: User | null;
  tokens: AuthTokens | null;
  tenant: TenantConfig | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // Actions
  setAuth: (user: User, tokens: AuthTokens, tenant: TenantConfig) => void;
  setUser: (user: User) => void;
  setTokens: (tokens: AuthTokens) => void;
  setTenant: (tenant: TenantConfig) => void;
  logout: () => void;
  setLoading: (loading: boolean) => void;
  
  // Computed
  hasRole: (role: string) => boolean;
  hasPermission: (screenId: string) => boolean;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      tokens: null,
      tenant: null,
      isAuthenticated: false,
      isLoading: false,

      // Actions
      setAuth: (user, tokens, tenant) => {
        set({
          user,
          tokens,
          tenant,
          isAuthenticated: true,
          isLoading: false,
        });
      },

      setUser: (user) => {
        set({ user });
      },

      setTokens: (tokens) => {
        set({ tokens });
      },

      setTenant: (tenant) => {
        set({ tenant });
      },

      logout: () => {
        set({
          user: null,
          tokens: null,
          tenant: null,
          isAuthenticated: false,
          isLoading: false,
        });
      },

      setLoading: (isLoading) => {
        set({ isLoading });
      },

      // Computed values
      hasRole: (role) => {
        const { user } = get();
        if (!user) return false;
        
        // Super admin has all roles
        if (user.role === 'SuperAdmin') return true;
        
        // Admin has admin and user roles
        if (user.role === 'Admin' && (role === 'Admin' || role === 'User')) return true;
        
        // Exact role match
        return user.role === role;
      },

      hasPermission: (screenId) => {
        const { user, tenant } = get();
        if (!user || !tenant) return false;
        
        const screen = tenant.screens.find(s => s.id === screenId);
        if (!screen) return false;
        
        return screen.permissions.includes(user.role as any);
      },
    }),
    {
      name: 'flowbit-auth',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        tenant: state.tenant,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Selectors for better performance
export const useAuth = () => useAuthStore((state) => ({
  user: state.user,
  tokens: state.tokens,
  tenant: state.tenant,
  isAuthenticated: state.isAuthenticated,
  isLoading: state.isLoading,
}));

export const useAuthActions = () => useAuthStore((state) => ({
  setAuth: state.setAuth,
  setUser: state.setUser,
  setTokens: state.setTokens,
  setTenant: state.setTenant,
  logout: state.logout,
  setLoading: state.setLoading,
}));

export const usePermissions = () => useAuthStore((state) => ({
  hasRole: state.hasRole,
  hasPermission: state.hasPermission,
}));
