import { useQuery, useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-hot-toast';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { authService, LoginRequest, UpdateProfileRequest } from '../services/auth';
import { User, TenantConfig } from '@shared/types';

// Query keys
export const AUTH_QUERY_KEYS = {
  profile: 'auth.profile',
  screens: 'auth.screens',
  stats: 'auth.stats',
  auditLogs: 'auth.auditLogs',
} as const;

/**
 * Hook for authentication operations
 */
export const useAuth = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const {
    user,
    tokens,
    tenant,
    isAuthenticated,
    isLoading,
    setAuth,
    setUser,
    setTenant,
    logout: logoutStore,
    setLoading,
    hasRole,
    hasPermission,
  } = useAuthStore();

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: authService.login,
    onMutate: () => {
      setLoading(true);
    },
    onSuccess: async (data) => {
      // Get user screens to build tenant config
      try {
        const screensData = await authService.getUserScreens();
        
        const tenantConfig: TenantConfig = {
          id: data.user.customerId,
          name: screensData.tenant.name,
          theme: screensData.tenant.theme,
          primaryColor: screensData.tenant.primaryColor,
          secondaryColor: screensData.tenant.secondaryColor,
          logo: screensData.tenant.logo,
          screens: screensData.screens,
          features: {}, // Will be populated from API if needed
          settings: {}, // Will be populated from API if needed
        };

        setAuth(data.user, data.tokens, tenantConfig);
        
        // Apply theme
        applyTheme(tenantConfig);
        
        toast.success(`Welcome back, ${data.user.profile.firstName}!`);
        navigate('/dashboard');
      } catch (error) {
        console.error('Failed to get user screens:', error);
        toast.error('Login successful, but failed to load user configuration');
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || error.message || 'Login failed';
      toast.error(message);
    },
    onSettled: () => {
      setLoading(false);
    },
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: () => authService.logout(tokens?.refreshToken || ''),
    onSuccess: () => {
      logoutStore();
      queryClient.clear();
      removeTheme();
      toast.success('Logged out successfully');
      navigate('/login');
    },
    onError: (error: any) => {
      // Still logout locally even if API call fails
      logoutStore();
      queryClient.clear();
      removeTheme();
      console.error('Logout error:', error);
      navigate('/login');
    },
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: authService.updateProfile,
    onSuccess: (updatedUser) => {
      setUser(updatedUser);
      queryClient.setQueryData(AUTH_QUERY_KEYS.profile, updatedUser);
      toast.success('Profile updated successfully');
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Failed to update profile';
      toast.error(message);
    },
  });

  // Get user profile query
  const profileQuery = useQuery({
    queryKey: AUTH_QUERY_KEYS.profile,
    queryFn: authService.getProfile,
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    onSuccess: (data) => {
      setUser(data);
    },
  });

  // Get user screens query
  const screensQuery = useQuery({
    queryKey: AUTH_QUERY_KEYS.screens,
    queryFn: authService.getUserScreens,
    enabled: isAuthenticated,
    staleTime: 10 * 60 * 1000, // 10 minutes
    onSuccess: (data) => {
      if (tenant) {
        const updatedTenant: TenantConfig = {
          ...tenant,
          screens: data.screens,
        };
        setTenant(updatedTenant);
      }
    },
  });

  // Get user stats query
  const statsQuery = useQuery({
    queryKey: AUTH_QUERY_KEYS.stats,
    queryFn: authService.getUserStats,
    enabled: isAuthenticated,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Apply theme to document
  const applyTheme = (tenantConfig: TenantConfig) => {
    const root = document.documentElement;
    
    // Apply theme class
    root.className = root.className.replace(/theme-\w+/g, '');
    root.classList.add(`theme-${tenantConfig.theme}`);
    
    // Apply custom colors if provided
    if (tenantConfig.primaryColor) {
      root.style.setProperty('--color-primary-500', tenantConfig.primaryColor);
    }
    if (tenantConfig.secondaryColor) {
      root.style.setProperty('--color-secondary-500', tenantConfig.secondaryColor);
    }
    
    // Update favicon if provided
    if (tenantConfig.favicon) {
      const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
      if (favicon) {
        favicon.href = tenantConfig.favicon;
      }
    }
  };

  // Remove theme from document
  const removeTheme = () => {
    const root = document.documentElement;
    root.className = root.className.replace(/theme-\w+/g, '');
    
    // Reset to default colors
    root.style.removeProperty('--color-primary-500');
    root.style.removeProperty('--color-secondary-500');
  };

  // Initialize auth on app start
  const initializeAuth = async () => {
    if (!isAuthenticated || !tokens) {
      return;
    }

    try {
      setLoading(true);
      
      // Check if token is still valid
      if (!authService.isTokenValid(tokens.accessToken)) {
        logoutStore();
        return;
      }

      // Refresh user data
      await queryClient.prefetchQuery({
        queryKey: AUTH_QUERY_KEYS.profile,
        queryFn: authService.getProfile,
      });

      // Apply theme if tenant is available
      if (tenant) {
        applyTheme(tenant);
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
      logoutStore();
    } finally {
      setLoading(false);
    }
  };

  return {
    // State
    user,
    tokens,
    tenant,
    isAuthenticated,
    isLoading: isLoading || loginMutation.isLoading || logoutMutation.isLoading,
    
    // Queries
    profile: profileQuery.data,
    screens: screensQuery.data?.screens || [],
    stats: statsQuery.data,
    
    // Query states
    isProfileLoading: profileQuery.isLoading,
    isScreensLoading: screensQuery.isLoading,
    isStatsLoading: statsQuery.isLoading,
    
    // Actions
    login: loginMutation.mutate,
    logout: logoutMutation.mutate,
    updateProfile: updateProfileMutation.mutate,
    initializeAuth,
    
    // Permissions
    hasRole,
    hasPermission,
    
    // Utilities
    isTokenValid: (token: string) => authService.isTokenValid(token),
    getTokenExpiration: (token: string) => authService.getTokenExpiration(token),
  };
};
