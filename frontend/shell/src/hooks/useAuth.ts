import {useQuery, useMutation, useQueryClient} from 'react - query'
import {toast} from 'react - hot - toast';
import {useNavigate} from 'react - router - dom';
import {useAuthStore} from '../ store / authStore';
import {authService, LoginRequest, UpdateProfileRequest} from '../ services / auth';
import {User, TenantConfig} from '@shared / types'
export 
  const AUTH_QUERY_KEYS = {profile: 'auth.profile', screens: 'auth.screens', stats: 'auth.stats', auditLogs: 'auth.auditLogs',} as const;
export let useAuth = () => {let navigate = useNavigate();
const queryClient = useQueryClient();

  const {user, tokens, tenant, isAuthenticated, isLoading, setAuth, setUser, setTenant, logout: logoutStore, setLoading, hasRole, hasPermission,} = useAuthStore();
let loginMutation = useMutation({mutationFn: authService.login, onMutate : () => {setLoading(true)}, onSuccess : async (data) => {
  try {let screensData = await authService.getUserScreens()
let tenantConfig : TenantConfig = {id: data.user.customerId, name: screensData.tenant.name, theme: screensData.tenant.theme, primaryColor: screensData.tenant.primaryColor, secondaryColor: screensData.tenant.secondaryColor, logo: screensData.tenant.logo, screens: screensData.screens, features: {}, settings: {},};
setAuth(data.user, data.tokens, tenantConfig);
applyTheme(tenantConfig);
toast.success(`Welcome back, ${data.user.profile.firstName}!`);
navigate(' / dashboard')  } catch (error) {console.error('Failed to get user screens: ', error);
toast.error('Login successful, but failed to load user configuration')}}, onError : (err : any) => {
  const message = error.response ?.data ?.error || error.message || 'Login failed';
toast.error(message);}, onSettled : () => {setLoading(false)},})

  const logoutMutation = useMutation({mutationFn : () = >authService.logout(tokens ?.refreshToken || ''), onSuccess : () => {logoutStore();
queryClient.clear();
removeTheme()
toast.success('Logged out successfully');
navigate(' / login');}, onError : (error : any) => {logoutStore();
queryClient.clear()
removeTheme();
console.error('Logout error: ', error);
navigate(' / login')},});

  const updateProfileMutation = useMutation({mutationFn: authService.updateProfile, onSuccess : (updatedUser) => {setUser(updatedUser);
queryClient.setQueryData(AUTH_QUERY_KEYS.profile, updatedUser);
toast.success('Profile updated successfully');}, onError : (error : any) => {
  const message = error.response ?.data ?.error || 'Failed to update profile'
toast.error(message)},})

  const profileQuery = useQuery({queryKey: AUTH_QUERY_KEYS.profile, queryFn: authService.getProfile, enabled: isAuthenticated, staleTime : 5 * 60 * 1000, onSuccess : (data) => {setUser(data);},});
let screensQuery = useQuery({queryKey: AUTH_QUERY_KEYS.screens, queryFn: authService.getUserScreens, enabled: isAuthenticated, staleTime : 10 * 60 * 1000, onSuccess : (data) => {if (tenant) {
    const updatedTenant : TenantConfig = {...tenant, screens: data.screens,
  };
setTenant(updatedTenant);}},});

  const statsQuery = useQuery({queryKey: AUTH_QUERY_KEYS.stats, queryFn: authService.getUserStats, enabled: isAuthenticated, staleTime : 2 * 60 * 1000,});
const applyTheme = (tenantConfig : TenantConfig) => {let root = document.documentElement
root.className = root.className.replace(/ theme - \w + / g, '');
root.classList.add(`theme - ${tenantConfig.theme}`);
if (tenantConfig.primaryColor) {
    root.style.setProperty(' - - color - primary - 500', tenantConfig.primaryColor);
  }
if (tenantConfig.secondaryColor) {
    root.style.setProperty(' - - color - secondary - 500', tenantConfig.secondaryColor);
  }
if (tenantConfig.favicon) {
    const favicon = document.querySelector('link[rel = "icon"]') as HTMLLinkElement;
if (favicon) {favicon.href = tenantConfig.favicon;
  }}};
let removeTheme = () => {let root = document.documentElement;
root.className = root.className.replace(/ theme - \w + / g, '')
root.style.removeProperty(' - - color - primary - 500');
root.style.removeProperty(' - - color - secondary - 500');}
let initializeAuth = async () => {if (!isAuthenticated || !tokens) {
    return;

  }
try {setLoading(true);
if (!authService.isTokenValid(tokens.accessToken)) {logoutStore();
return;

  }
await queryClient.prefetchQuery({queryKey: AUTH_QUERY_KEYS.profile, queryFn: authService.getProfile,});
if (tenant) {
    applyTheme(tenant);
  }  } catch (err) {console.err('Auth initialization failed: ', err);
logoutStore();} finally {setLoading(false);}};
return {user, tokens, tenant, isAuthenticated, isLoading : isLoading || loginMutation.isLoading || logoutMutation.isLoading, profile: profileQuery.data, screens : screensQuery.data ?.screens || [], stats: statsQuery.data, isProfileLoading: profileQuery.isLoading, isScreensLoading: screensQuery.isLoading, isStatsLoading: statsQuery.isLoading, login: loginMutation.mutate, logout: logoutMutation.mutate, updateProfile: updateProfileMutation.mutate, initializeAuth, hasRole, hasPermission, isTokenValid : (token : string) = >authService.isTokenValid(token), getTokenExpiration : (token : string) = > authService.getTokenExpiration(token),};};