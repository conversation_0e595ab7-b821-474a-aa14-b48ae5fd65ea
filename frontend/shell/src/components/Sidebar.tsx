import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import clsx from 'clsx';
import {
  HomeIcon,
  TicketIcon,
  UserIcon,
  Cog6ToothIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { User, TenantConfig, ScreenConfig } from '@shared/types';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  user: User;
  tenant: TenantConfig;
}

// Icon mapping for screens
const iconMap: Record<string, React.ComponentType<any>> = {
  home: HomeIcon,
  dashboard: HomeIcon,
  ticket: TicketIcon,
  'support-tickets': TicketIcon,
  user: UserIcon,
  profile: UserIcon,
  admin: Cog6ToothIcon,
  'admin-dashboard': Cog6ToothIcon,
  settings: Cog6ToothIcon,
};

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose, user, tenant }) => {
  const location = useLocation();

  // Get navigation items from tenant screens
  const getNavigationItems = () => {
    const items = [
      {
        id: 'dashboard',
        name: 'Dashboard',
        href: '/dashboard',
        icon: HomeIcon,
        current: location.pathname === '/dashboard',
      },
    ];

    // Add tenant-specific screens
    tenant.screens.forEach((screen: ScreenConfig) => {
      const Icon = iconMap[screen.icon] || HomeIcon;
      
      items.push({
        id: screen.id,
        name: screen.name,
        href: screen.url,
        icon: Icon,
        current: location.pathname.startsWith(screen.url),
        badge: screen.badge?.enabled ? screen.badge : undefined,
      });
    });

    // Add profile link
    items.push({
      id: 'profile',
      name: 'Profile',
      href: '/profile',
      icon: UserIcon,
      current: location.pathname === '/profile',
    });

    return items;
  };

  const navigationItems = getNavigationItems();

  return (
    <>
      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white border-r border-gray-200 px-6 pb-4">
          {/* Logo */}
          <div className="flex h-16 shrink-0 items-center">
            <img
              className="h-8 w-auto"
              src={tenant.logo}
              alt={tenant.name}
              onError={(e) => {
                // Fallback to default logo if tenant logo fails to load
                (e.target as HTMLImageElement).src = '/logo.svg';
              }}
            />
            <span className="ml-3 text-xl font-semibold text-gray-900">
              {tenant.name}
            </span>
          </div>

          {/* Navigation */}
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {navigationItems.map((item) => (
                    <li key={item.id}>
                      <NavLink
                        to={item.href}
                        className={({ isActive }) =>
                          clsx(
                            'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition-colors',
                            isActive || item.current
                              ? 'bg-primary-50 text-primary-700'
                              : 'text-gray-700 hover:text-primary-700 hover:bg-gray-50'
                          )
                        }
                      >
                        <item.icon
                          className={clsx(
                            'h-6 w-6 shrink-0',
                            item.current
                              ? 'text-primary-700'
                              : 'text-gray-400 group-hover:text-primary-700'
                          )}
                          aria-hidden="true"
                        />
                        <span className="flex-1">{item.name}</span>
                        {item.badge && item.badge.count && item.badge.count > 0 && (
                          <span
                            className={clsx(
                              'ml-3 inline-block py-0.5 px-2 text-xs rounded-full',
                              item.badge.color === 'red'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-gray-100 text-gray-800'
                            )}
                          >
                            {item.badge.count}
                          </span>
                        )}
                      </NavLink>
                    </li>
                  ))}
                </ul>
              </li>

              {/* User info at bottom */}
              <li className="mt-auto">
                <div className="flex items-center gap-x-4 px-2 py-3 text-sm font-semibold leading-6 text-gray-900">
                  <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                    {user.profile.avatar ? (
                      <img
                        className="h-8 w-8 rounded-full"
                        src={user.profile.avatar}
                        alt={`${user.profile.firstName} ${user.profile.lastName}`}
                      />
                    ) : (
                      <span className="text-primary-700 font-medium">
                        {user.profile.firstName[0]}{user.profile.lastName[0]}
                      </span>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {user.profile.firstName} {user.profile.lastName}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {user.role}
                    </p>
                  </div>
                </div>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      {/* Mobile sidebar */}
      <div
        className={clsx(
          'relative z-50 lg:hidden',
          isOpen ? 'block' : 'hidden'
        )}
      >
        <div className="fixed inset-0 flex">
          <div className="relative mr-16 flex w-full max-w-xs flex-1">
            <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
              <button
                type="button"
                className="-m-2.5 p-2.5"
                onClick={onClose}
              >
                <span className="sr-only">Close sidebar</span>
                <XMarkIcon className="h-6 w-6 text-white" aria-hidden="true" />
              </button>
            </div>

            <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4">
              {/* Mobile logo */}
              <div className="flex h-16 shrink-0 items-center">
                <img
                  className="h-8 w-auto"
                  src={tenant.logo}
                  alt={tenant.name}
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/logo.svg';
                  }}
                />
                <span className="ml-3 text-xl font-semibold text-gray-900">
                  {tenant.name}
                </span>
              </div>

              {/* Mobile navigation */}
              <nav className="flex flex-1 flex-col">
                <ul role="list" className="flex flex-1 flex-col gap-y-7">
                  <li>
                    <ul role="list" className="-mx-2 space-y-1">
                      {navigationItems.map((item) => (
                        <li key={item.id}>
                          <NavLink
                            to={item.href}
                            onClick={onClose}
                            className={({ isActive }) =>
                              clsx(
                                'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold',
                                isActive || item.current
                                  ? 'bg-primary-50 text-primary-700'
                                  : 'text-gray-700 hover:text-primary-700 hover:bg-gray-50'
                              )
                            }
                          >
                            <item.icon
                              className={clsx(
                                'h-6 w-6 shrink-0',
                                item.current
                                  ? 'text-primary-700'
                                  : 'text-gray-400 group-hover:text-primary-700'
                              )}
                              aria-hidden="true"
                            />
                            <span className="flex-1">{item.name}</span>
                            {item.badge && item.badge.count && item.badge.count > 0 && (
                              <span
                                className={clsx(
                                  'ml-3 inline-block py-0.5 px-2 text-xs rounded-full',
                                  item.badge.color === 'red'
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-gray-100 text-gray-800'
                                )}
                              >
                                {item.badge.count}
                              </span>
                            )}
                          </NavLink>
                        </li>
                      ))}
                    </ul>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
