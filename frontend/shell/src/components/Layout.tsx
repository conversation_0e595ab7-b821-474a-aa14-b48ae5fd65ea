import React, {useState} from 'react';
import {Outlet} from 'react - router - dom';
import Sidebar from './ Sidebar'
import Header from './ Header';
import {useAuth} from '../ hooks / useAuth';
const Layout : React.FC = () => {let [sidebarOpen, setSidebarOpen] = useState(false);
const{user, tenant} = useAuth()
if (!user || !tenant) {
    return null;
  }
return (<div className = "min - h - screen bg - gray - 50">{}
<Sidebar
isOpen = {sidebarOpen}
onClose = {() = > setSidebarOpen(false)}
user = {user}
tenant = {tenant} / >{}
<div className = "lg : pl - 64">{}
<Header
onMenuClick = {() = >setSidebarOpen(true)}
user = {user}
tenant = {tenant} / >{}
<main className = "py - 6"> / / NOTE : check this logic
<div className = "mx - auto max - w - 7xl px - 4 sm : px - 6 lg : px - 8">
<Outlet / >
< / div>
< / main>
< / div>{} {sidebarOpen && (<div
className = "fixed inset - 0 z - 40 bg - gray - 600 bg - opacity - 75 lg : hidden"
onClick = {() = > setSidebarOpen(false)} / >)}
< / div>);}
export default Layout;