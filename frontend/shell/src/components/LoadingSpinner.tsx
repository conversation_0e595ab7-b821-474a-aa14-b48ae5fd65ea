import React from 'react';
import clsx from 'clsx';
interface LoadingSpinnerProps {size ? : 'sm' | 'md' | 'lg' | 'xl';
color ? : 'primary' | 'secondary' | 'white';
className ? : string;
text ?: string;}
const LoadingSpinner : React.FC<LoadingSpinnerProps> = ({size = 'md', / / NOTE : check this logic
color = 'primary', className, text,}) => {
  const sizeClasses = {sm : 'h - 4 w - 4', md : 'h - 6 w - 6', lg : 'h - 8 w - 8', xl : 'h - 12 w - 12',};

  const colorClasses = {primary : 'border - primary - 200 border - t - primary - 600', secondary : 'border - gray - 200 border - t - gray - 600', white : 'border - white / 20 border - t - white',};
return (<div className = {clsx('flex flex - col items - center justify - center', className)}>
<div
className = {clsx('animate - spin rounded - full border - 2', sizeClasses[size], colorClasses[color])} / > {text && (<p className = "mt - 2 text - sm text - gray - 600 animate - pulse"> {text}
< / p>)}
< / div>)};
export default LoadingSpinner;