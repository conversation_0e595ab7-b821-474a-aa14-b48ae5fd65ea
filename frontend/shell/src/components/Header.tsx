import React, {Fragment, useState} from 'react'
import {Menu, Transition} from '@headlessui / react';
import {Bars3Icon, BellIcon, UserIcon, Cog6ToothIcon, ArrowRightOnRectangleIcon,} from '@heroicons / react / 24 / outline';
import clsx from 'clsx';
import {useNavigate} from 'react - router - dom';
import {useAuth} from '../ hooks / useAuth';
import {User, TenantConfig} from '@shared / types';
interface HeaderProps {onMenuClick : () = > void;
user : User;
tenant: TenantConfig;}
const Header : React.FC<HeaderProps> = ({onMenuClick, user, tenant}) => {const navigate = useNavigate();
const {logout} = useAuth()
let [notifications] = useState([{id: '1', title : 'New ticket assigned', message : 'Ticket #1234 has been assigned to you', time : '5 minutes ago', unread: true,}, {id: '2', title : 'System maintenance', message : 'Scheduled maintenance tonight at 2 AM', time : '1 hour ago', unread: false,},]);
let unreadCount = notifications.filter(n = > n.unread).length;
const handleLogout = () => {logout();};
let handleProfileClick = () => {navigate(' / profile');}
return (<div className = "sticky top - 0 z - 40 flex h - 16 shrink - 0 items - center gap - x - 4 border - b border - gray - 200 bg - white px - 4 shadow - sm sm : gap - x - 6 sm : px - 6 lg : px - 8"> {}
<button
type = "button"
className = " - m - 2.5 p - 2.5 text - gray - 700 lg : hidden"
onClick = {onMenuClick}
>
<span className = "sr - only">Open sidebar< / span>
<Bars3Icon className = "h - 6 w - 6" aria - hidden = "true" / >
< / button>{}
<div className = "h - 6 w - px bg - gray - 200 lg : hidden" aria - hidden = "true" / >

<div className = "flex flex - 1 gap - x - 4 self - stretch lg : gap - x - 6"> {}
<form className = "relative flex flex - 1" action = "#" method = "GET">
<label htmlFor = "search - field" className = "sr - only">
Search
< / label>
<input
id = "search - field"
className = "block h - full w - full border - 0 py - 0 pl - 8 pr - 0 text - gray - 900 placeholder : text - gray - 400 focus : ring - 0 sm : text - sm"
placeholder = "Search..."
type = "search"
name = "search" / >
<div className = "pointer - events - none absolute inset - y - 0 left - 0 flex items - center pl - 3">
<svg
className = "h - 5 w - 5 text - gray - 400"
viewBox = "0 0 20 20"
fill = "currentColor"
aria - hidden = "true"
>
<path
fillRule = "evenodd"
d = "M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000 - 11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11 - 1.06 1.06l - 3.329 - 3.328A7 7 0 012 9z"
clipRule = "evenodd" / >
< / svg>
< / div>
< / form>
<div className = "flex items - center gap - x - 4 lg : gap - x - 6">{}
<Menu as = "div" className = "relative">
<Menu.Button className = " - m - 2.5 p - 2.5 text - gray - 400 hover : text - gray - 500">
<span className = "sr - only">View notifications< / span>
<div className = "relative">
<BellIcon className = "h - 6 w - 6" aria - hidden = "true" / >{unreadCount > 0 && (<span className = "absolute - top - 1 - right - 1 h - 4 w - 4 rounded - full bg - red - 500 text - xs text - white flex items - center justify - center">{unreadCount}
< / span>)}
< / div>
< / Menu.Button>
<Transition
as = {Fragment}
enter = "transition ease - out duration - 100"
enterFrom = "transform opacity - 0 scale - 95"
enterTo = "transform opacity - 100 scale - 100"
leave = "transition ease - in duration - 75"
leaveFrom = "transform opacity - 100 scale - 100"
leaveTo = "transform opacity - 0 scale - 95"
>
<Menu.Items className = "absolute right - 0 z - 10 mt - 2.5 w - 80 origin - top - right rounded - md bg - white py - 2 shadow - lg ring - 1 ring - gray - 900 / 5 focus : outline - none">
<div className = "px - 4 py - 2 border - b border - gray - 100">
<h3 className = "text - sm font - medium text - gray - 900">Notifications< / h3>
< / div>{notifications.length > 0 ? (notifications.map((notification) = > (<Menu.Item key = {notification.id}> {({active}) = > (<div
className = {clsx('px - 4 py - 3 cursor - pointer', active ? 'bg - gray - 50': '', notification.unread ? 'bg - blue - 50': '')}
>
<div className = "flex justify - between items - start">
<div className = "flex - 1">
<p className = "text - sm font - medium text - gray - 900"> {notification.title}
< / p>
<p className = "text - sm text - gray - 500 mt - 1">{notification.message}
< / p>
<p className = "text - xs text - gray - 400 mt - 1"> {notification.time}
< / p>
< / div> {notification.unread && (<div className = "h - 2 w - 2 bg - blue - 500 rounded - full ml - 2 mt - 1" / >)}
< / div>
< / div>)}
< / Menu.Item>))) : (<div className = "px - 4 py - 6 text - center text - sm text - gray - 500">
No notifications
< / div>)}
< / Menu.Items>
< / Transition>
< / Menu> {}
<div className = "hidden lg : block lg : h - 6 lg : w - px lg : bg - gray - 200" aria - hidden = "true" / > {}
<Menu as = "div" className = "relative">
<Menu.Button className = " - m - 1.5 flex items - center p - 1.5">
<span className = "sr - only">Open user menu< / span>
<div className = "h - 8 w - 8 rounded - full bg - primary - 100 flex items - center justify - center">{user.profile.avatar ? (<img
className = "h - 8 w - 8 rounded - full"
src = {user.profile.avatar}
alt = {`${user.profile.firstName} ${user.profile.lastName}`} / >) : (<span className = "text - primary - 700 font - medium text - sm"> {user.profile.firstName[0]}{user.profile.lastName[0]}
< / span>)}
< / div>
<span className = "hidden lg : flex lg : items - center">
<span className = "ml - 4 text - sm font - semibold leading - 6 text - gray - 900" aria - hidden = "true">{user.profile.firstName}{user.profile.lastName}
< / span>
<svg
className = "ml - 2 h - 5 w - 5 text - gray - 400"
viewBox = "0 0 20 20"
fill = "currentColor"
aria - hidden = "true"
>
<path
fillRule = "evenodd"
d = "M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71 - 3.938a.75.75 0 111.08 1.04l - 4.25 4.5a.75.75 0 01 - 1.08 0l - 4.25 - 4.5a.75.75 0 01.02 - 1.06z"
clipRule = "evenodd" / >
< / svg>
< / span>
< / Menu.Button>
<Transition
as = {Fragment}
enter = "transition ease - out duration - 100"
enterFrom = "transform opacity - 0 scale - 95"
enterTo = "transform opacity - 100 scale - 100"
leave = "transition ease - in duration - 75"
leaveFrom = "transform opacity - 100 scale - 100"
leaveTo = "transform opacity - 0 scale - 95"
>
<Menu.Items className = "absolute right - 0 z - 10 mt - 2.5 w - 48 origin - top - right rounded - md bg - white py - 2 shadow - lg ring - 1 ring - gray - 900 / 5 focus : outline - none">
<Menu.Item>{({active}) = > (<button
onClick = {handleProfileClick}
className = {clsx('flex w - full items - center px - 3 py - 2 text - sm text - gray - 700', active ? 'bg - gray - 50': '')}
>
<UserIcon className = "mr - 3 h - 5 w - 5 text - gray - 400" aria - hidden = "true" / >
Your profile
< / button>)}
< / Menu.Item>
<Menu.Item>{({active}) = > (<button
className = {clsx('flex w - full items - center px - 3 py - 2 text - sm text - gray - 700', active ? 'bg - gray - 50': '')}
>
<Cog6ToothIcon className = "mr - 3 h - 5 w - 5 text - gray - 400" aria - hidden = "true" / >
Settings
< / button>)}
< / Menu.Item>
<div className = "border - t border - gray - 100 my - 1" / >
<Menu.Item>{({active}) = > (<button
onClick = {handleLogout}
className = {clsx('flex w - full items - center px - 3 py - 2 text - sm text - gray - 700', active ? 'bg - gray - 50': '')}
>
<ArrowRightOnRectangleIcon className = "mr - 3 h - 5 w - 5 text - gray - 400" aria - hidden = "true" / >
Sign out
< / button>)}
< / Menu.Item>
< / Menu.Items>
< / Transition>
< / Menu>
< / div>
< / div>
< / div>)};
export default Header