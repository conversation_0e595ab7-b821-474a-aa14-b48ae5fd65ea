import React, { useEffect, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './hooks/useAuth';
import ProtectedRoute from './components/ProtectedRoute';
import LoadingSpinner from './components/LoadingSpinner';
import Layout from './components/Layout';
import { UserRole } from '@shared/types';

// Lazy load pages
const LoginPage = React.lazy(() => import('./pages/LoginPage'));
const DashboardPage = React.lazy(() => import('./pages/DashboardPage'));
const ProfilePage = React.lazy(() => import('./pages/ProfilePage'));
const UnauthorizedPage = React.lazy(() => import('./pages/UnauthorizedPage'));
const NotFoundPage = React.lazy(() => import('./pages/NotFoundPage'));

// Lazy load micro-frontends
const SupportTicketsApp = React.lazy(() => import('supportTickets/App'));
const AdminDashboardApp = React.lazy(() => import('adminDashboard/App'));

// Loading fallback component
const PageLoadingFallback = () => (
  <div className="min-h-screen flex items-center justify-center">
    <LoadingSpinner size="lg" text="Loading..." />
  </div>
);

// Micro-frontend error fallback
const MicroFrontendErrorFallback = ({ error, name }: { error: Error; name: string }) => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="text-center">
      <div className="mb-4">
        <svg className="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h1 className="text-xl font-semibold text-gray-900 mb-2">
        Failed to load {name}
      </h1>
      <p className="text-gray-600 mb-4">
        The {name} module could not be loaded. Please try refreshing the page.
      </p>
      <button
        onClick={() => window.location.reload()}
        className="btn-primary"
      >
        Refresh Page
      </button>
    </div>
  </div>
);

// Micro-frontend wrapper with error boundary
const MicroFrontendWrapper: React.FC<{
  children: React.ReactNode;
  name: string;
}> = ({ children, name }) => {
  return (
    <React.Suspense
      fallback={<PageLoadingFallback />}
    >
      <ErrorBoundary
        fallback={(error) => <MicroFrontendErrorFallback error={error} name={name} />}
      >
        {children}
      </ErrorBoundary>
    </React.Suspense>
  );
};

// Simple error boundary for micro-frontends
class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback: (error: Error) => React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Micro-frontend error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError && this.state.error) {
      return this.props.fallback(this.state.error);
    }

    return this.props.children;
  }
}

const App: React.FC = () => {
  const { isAuthenticated, isLoading, initializeAuth, user, tenant } = useAuth();

  // Initialize authentication on app start
  useEffect(() => {
    initializeAuth();
  }, []);

  // Show loading screen during initial auth check
  if (isLoading) {
    return <PageLoadingFallback />;
  }

  return (
    <div className="App">
      <Suspense fallback={<PageLoadingFallback />}>
        <Routes>
          {/* Public routes */}
          <Route
            path="/login"
            element={
              isAuthenticated ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <LoginPage />
              )
            }
          />

          {/* Protected routes */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }
          >
            {/* Dashboard */}
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<DashboardPage />} />

            {/* Profile */}
            <Route path="profile" element={<ProfilePage />} />

            {/* Support Tickets Micro-frontend */}
            <Route
              path="support-tickets/*"
              element={
                <ProtectedRoute requiredPermission="support-tickets">
                  <MicroFrontendWrapper name="Support Tickets">
                    <SupportTicketsApp
                      user={user!}
                      tenant={tenant!}
                      onNavigate={(path) => window.history.pushState(null, '', path)}
                    />
                  </MicroFrontendWrapper>
                </ProtectedRoute>
              }
            />

            {/* Admin Dashboard Micro-frontend */}
            <Route
              path="admin/*"
              element={
                <ProtectedRoute
                  requiredRoles={[UserRole.ADMIN, UserRole.SUPER_ADMIN]}
                  requiredPermission="admin-dashboard"
                >
                  <MicroFrontendWrapper name="Admin Dashboard">
                    <AdminDashboardApp
                      user={user!}
                      tenant={tenant!}
                      onNavigate={(path) => window.history.pushState(null, '', path)}
                    />
                  </MicroFrontendWrapper>
                </ProtectedRoute>
              }
            />
          </Route>

          {/* Error pages */}
          <Route path="/unauthorized" element={<UnauthorizedPage />} />
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </Suspense>
    </div>
  );
};

export default App;
