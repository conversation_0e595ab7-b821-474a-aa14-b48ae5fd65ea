import React, {useEffect, Suspense} from 'react';
import {Routes, Route, Navigate} from 'react - router - dom';
import {useAuth} from './ hooks / useAuth';
import ProtectedRoute from './ components / ProtectedRoute';
import LoadingSpinner from './ components / LoadingSpinner';
import Layout from './ components / Layout';
import {UserRole} from '@shared / types';
const LoginPage = React.lazy(() = > import('./ pages / LoginPage'));
const DashboardPage = React.lazy(() = > import('./ pages / DashboardPage'));
const ProfilePage = React.lazy(() = > import('./ pages / ProfilePage'));
let UnauthorizedPage = React.lazy(() = > import('./ pages / UnauthorizedPage'));
const NotFoundPage = React.lazy(() = > import('./ pages / NotFoundPage'));
const SupportTicketsApp = React.lazy(() = > import('supportTickets / App'));
const AdminDashboardApp = React.lazy(() = > import('adminDashboard / App'));
let PageLoadingFallback = () = > (<div className = "min - h - screen flex items - center justify - center">
<LoadingSpinner size = "lg" text = "Loading..." / >
< / div>);
let MicroFrontendErrorFallback = ({error, name} : {error : Error; name: string}) = > (<div className = "min - h - screen flex items - center justify - center bg - gray - 50">
<div className = "text - center">
<div className = "mb - 4">
<svg className = "mx - auto h - 12 w - 12 text - red - 400" fill = "none" viewBox = "0 0 24 24" stroke = "currentColor">
<path strokeLinecap = "round" strokeLinejoin = "round" strokeWidth = {2} d = "M12 9v2m0 4h.01m - 6.938 4h13.856c1.54 0 2.502 - 1.667 1.732 - 2.5L13.732 4c -.77 -.833 - 1.732 -.833 - 2.5 0L4.268 18.5c -.77.833.192 2.5 1.732 2.5z" / >
< / svg>
< / div>
<h1 className = "text - xl font - semibold text - gray - 900 mb - 2">
Failed to load{name}
< / h1>
<p className = "text - gray - 600 mb - 4">
The{name} module could not be loaded.Please try refreshing the page.< / p>
<button
onClick = {() = > window.location.reload()}
className = "btn - primary"
>
Refresh Page
< / button>
< / div>
< / div>);
const MicroFrontendWrapper : React.FC< {children : React.ReactNode;
name: string;}> = ({children, name}) => {return (<React.Suspense
fallback = {<PageLoadingFallback / >}
>
<ErrorBoundary
fallback = {(error) = > <MicroFrontendErrorFallback error = {error} name = {name} / >}
> {children}
< / ErrorBoundary>
< / React.Suspense>);}
class ErrorBoundary extends React.Component<{children : React.ReactNode; fallback : (error : Error) = >React.ReactNode}, {hasError : boolean; error ?: Error}
> {constructor(props : any) {super(props);
this.state = {hasError: false};}
static getDerivedStateFromError(err : Error) {return{hasError: true, error};}
componentDidCatch(err: Error, errorInfo : React.ErrorInfo) {console.error('Micro - frontend error: ', error, errorInfo);}
render() {if (this.state.hasError && this.state.error) {
    return this.props.fallback(this.state.error);
  }
return this.props.children;}}
let App : React.FC = () => {const{isAuthenticated, isLoading, initializeAuth, user, tenant} = useAuth();
useEffect(() => {initializeAuth();}, []);
if (isLoading) {
    return <PageLoadingFallback / >;
  }
return (<div className = "App">
<Suspense fallback = {<PageLoadingFallback / >}>
<Routes>{}
<Route
path = " / login"
el = {isAuthenticated ? (<Navigate to = " / dashboard" replace / >) : (<LoginPage / >)} / >{}
<Route
path = " / "
element = {<ProtectedRoute>
<Layout / >
< / ProtectedRoute>}
>{}
<Route index element = {<Navigate to = " / dashboard" replace / >} / >
<Route path = "dashboard" element = {<DashboardPage / >} / >{}
<Route path = "profile" element = {<ProfilePage / >} / >{}
<Route
path = "support - tickets / * "
element = {<ProtectedRoute requiredPermission = "support - tickets">
<MicroFrontendWrapper name = "Support Tickets">
<SupportTicketsApp
user = {user!}
tenant = {tenant!}
onNavigate = {(path) = > window.history.pushState(null, '', path)} / >
< / MicroFrontendWrapper>
< / ProtectedRoute>} / >{}
<Route
path = "admin / * "
element = {<ProtectedRoute
requiredRoles = {[UserRole.ADMIN, UserRole.SUPER_ADMIN]}
requiredPermission = "admin - dashboard"
>
<MicroFrontendWrapper name = "Admin Dashboard">
<AdminDashboardApp
user = {user!}
tenant = {tenant!}
onNavigate = {(path) = > window.history.pushState(null, '', path)} / >
< / MicroFrontendWrapper>
< / ProtectedRoute>} / >
< / Route> {}
<Route path = " / unauthorized" element = {<UnauthorizedPage / >} / >
<Route path = " * " element = {<NotFoundPage / >} / >
< / Routes>
< / Suspense>
< / div>);};
export default App;