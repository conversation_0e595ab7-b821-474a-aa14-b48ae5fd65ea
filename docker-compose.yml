version: '3.8'

services:
  # MongoDB with replica set for transactions
  mongodb:
    image: mongo:7.0
    container_name: flowbit-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: flowbit
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./seed-data.js:/docker-entrypoint-initdb.d/seed-data.js:ro
    command: mongod --replSet rs0 --bind_ip_all
    networks:
      - flowbit-network

  # Redis for session storage and caching
  redis:
    image: redis:7.2-alpine
    container_name: flowbit-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - flowbit-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: flowbit-backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3001
      MONGODB_URI: ******************************************************************
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_REFRESH_SECRET: your-super-secret-refresh-key-change-in-production
      N8N_WEBHOOK_SECRET: n8n-webhook-secret-key
      CORS_ORIGIN: http://localhost:3000
    ports:
      - "3001:3001"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - flowbit-network

  # React Shell Application
  frontend-shell:
    build:
      context: ./frontend/shell
      dockerfile: Dockerfile
    container_name: flowbit-frontend-shell
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:3001/api
      REACT_APP_WS_URL: ws://localhost:3001
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/shell:/app
      - /app/node_modules
    networks:
      - flowbit-network

  # Support Tickets Micro-frontend
  frontend-tickets:
    build:
      context: ./frontend/support-tickets
      dockerfile: Dockerfile
    container_name: flowbit-frontend-tickets
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:3001/api
    ports:
      - "3002:3002"
    volumes:
      - ./frontend/support-tickets:/app
      - /app/node_modules
    networks:
      - flowbit-network

  # Admin Dashboard Micro-frontend
  frontend-admin:
    build:
      context: ./frontend/admin-dashboard
      dockerfile: Dockerfile
    container_name: flowbit-frontend-admin
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:3001/api
    ports:
      - "3003:3003"
    volumes:
      - ./frontend/admin-dashboard:/app
      - /app/node_modules
    networks:
      - flowbit-network

  # n8n Workflow Engine
  n8n:
    image: n8nio/n8n:latest
    container_name: flowbit-n8n
    restart: unless-stopped
    environment:
      N8N_BASIC_AUTH_ACTIVE: true
      N8N_BASIC_AUTH_USER: admin
      N8N_BASIC_AUTH_PASSWORD: password123
      N8N_HOST: 0.0.0.0
      N8N_PORT: 5678
      N8N_PROTOCOL: http
      WEBHOOK_URL: http://localhost:5678
      GENERIC_TIMEZONE: UTC
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: n8n
      DB_POSTGRESDB_USER: n8n
      DB_POSTGRESDB_PASSWORD: n8n123
    ports:
      - "5678:5678"
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./n8n/workflows:/home/<USER>/.n8n/workflows
    depends_on:
      - postgres
    networks:
      - flowbit-network

  # PostgreSQL for n8n
  postgres:
    image: postgres:15
    container_name: flowbit-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: n8n
      POSTGRES_USER: n8n
      POSTGRES_PASSWORD: n8n123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - flowbit-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: flowbit-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - frontend-shell
      - frontend-tickets
      - frontend-admin
    networks:
      - flowbit-network

  # ngrok for webhook testing (development only)
  ngrok:
    image: ngrok/ngrok:latest
    container_name: flowbit-ngrok
    restart: unless-stopped
    command: 
      - "start"
      - "--all"
      - "--config"
      - "/etc/ngrok.yml"
    volumes:
      - ./ngrok/ngrok.yml:/etc/ngrok.yml:ro
    ports:
      - "4040:4040"
    environment:
      NGROK_AUTHTOKEN: ${NGROK_AUTHTOKEN}
    networks:
      - flowbit-network

volumes:
  mongodb_data:
  redis_data:
  n8n_data:
  postgres_data:

networks:
  flowbit-network:
    driver: bridge
