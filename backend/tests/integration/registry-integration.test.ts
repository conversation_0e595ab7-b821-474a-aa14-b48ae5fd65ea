import request from 'supertest';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import fs from 'fs/promises';
import path from 'path';
import app from '../../src/app';
import User from '../../src/models/User';
import { UserRole } from '../../src/types';

describe('Registry Integration Tests', () => {
  let mongoServer: MongoMemoryServer;
  let server: any;
  let userToken: string;
  let adminToken: string;

  const mockRegistry = {
    tenants: {
      'test-tenant': {
        name: 'Test Tenant',
        theme: 'blue',
        primaryColor: '#3b82f6',
        secondaryColor: '#1e40af',
        logo: 'https://example.com/logo.png',
        favicon: '/favicon.ico',
        screens: [
          {
            id: 'support-tickets',
            name: 'Support Tickets',
            url: '/support-tickets',
            icon: 'ticket',
            permissions: ['User', 'Admin'],
            badge: { enabled: true, color: 'red' }
          },
          {
            id: 'admin-dashboard',
            name: 'Admin Dashboard',
            url: '/admin',
            icon: 'dashboard',
            permissions: ['Admin'],
            badge: { enabled: false }
          }
        ],
        features: {
          realTimeNotifications: true,
          auditLogging: true
        },
        settings: {
          timezone: 'UTC',
          language: 'en'
        }
      }
    },
    globalSettings: {
      maxUsersPerTenant: 100
    },
    version: '1.0.0',
    lastUpdated: '2024-01-01T00:00:00Z'
  };

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    process.env.MONGODB_URI = mongoUri;
    
    // Create mock registry file
    const registryPath = path.join(process.cwd(), 'registry.json');
    await fs.writeFile(registryPath, JSON.stringify(mockRegistry, null, 2));
    
    // Import app after setting environment
    const { default: application } = await import('../../src/app');
    server = application;
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
    
    // Clean up registry file
    try {
      const registryPath = path.join(process.cwd(), 'registry.json');
      await fs.unlink(registryPath);
    } catch (error) {
      // Ignore if file doesn't exist
    }
  });

  beforeEach(async () => {
    await User.deleteMany({});

    // Create test users
    const user = await User.create({
      customerId: 'test-tenant',
      email: '<EMAIL>',
      password: 'Password123!',
      role: UserRole.USER,
      profile: {
        firstName: 'Test',
        lastName: 'User'
      }
    });

    const admin = await User.create({
      customerId: 'test-tenant',
      email: '<EMAIL>',
      password: 'Password123!',
      role: UserRole.ADMIN,
      profile: {
        firstName: 'Test',
        lastName: 'Admin'
      }
    });

    // Get tokens
    const userLoginResponse = await request(server)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'Password123!',
        customerId: 'test-tenant'
      });

    const adminLoginResponse = await request(server)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'Password123!',
        customerId: 'test-tenant'
      });

    userToken = userLoginResponse.body.data.tokens.accessToken;
    adminToken = adminLoginResponse.body.data.tokens.accessToken;
  });

  describe('GET /api/me/screens', () => {
    it('should return screens filtered by user role', async () => {
      const response = await request(server)
        .get('/api/me/screens')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.screens).toHaveLength(1);
      expect(response.body.data.screens[0].id).toBe('support-tickets');
      expect(response.body.data.tenant.name).toBe('Test Tenant');
      expect(response.body.data.tenant.theme).toBe('blue');
    });

    it('should return all screens for admin user', async () => {
      const response = await request(server)
        .get('/api/me/screens')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.screens).toHaveLength(2);
      
      const screenIds = response.body.data.screens.map((s: any) => s.id);
      expect(screenIds).toContain('support-tickets');
      expect(screenIds).toContain('admin-dashboard');
    });

    it('should include badge counts for enabled badges', async () => {
      const response = await request(server)
        .get('/api/me/screens')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      const supportTicketsScreen = response.body.data.screens.find((s: any) => s.id === 'support-tickets');
      expect(supportTicketsScreen.badge).toBeDefined();
      expect(supportTicketsScreen.badge.enabled).toBe(true);
      expect(supportTicketsScreen.badge.count).toBeDefined();
    });

    it('should fail without authentication', async () => {
      const response = await request(server)
        .get('/api/me/screens');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/me/screens/:screenId/access', () => {
    it('should return true for authorized screen access', async () => {
      const response = await request(server)
        .get('/api/me/screens/support-tickets/access')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.hasAccess).toBe(true);
    });

    it('should return false for unauthorized screen access', async () => {
      const response = await request(server)
        .get('/api/me/screens/admin-dashboard/access')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.hasAccess).toBe(false);
    });

    it('should return false for non-existent screen', async () => {
      const response = await request(server)
        .get('/api/me/screens/non-existent/access')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.hasAccess).toBe(false);
    });
  });

  describe('GET /api/admin/config', () => {
    it('should return tenant configuration for admin', async () => {
      const response = await request(server)
        .get('/api/admin/config')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('Test Tenant');
      expect(response.body.data.theme).toBe('blue');
      expect(response.body.data.screens).toHaveLength(2);
    });

    it('should fail for non-admin user', async () => {
      const response = await request(server)
        .get('/api/admin/config')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /api/admin/config', () => {
    it('should update tenant configuration for admin', async () => {
      const updates = {
        name: 'Updated Test Tenant',
        theme: 'green',
        primaryColor: '#10b981'
      };

      const response = await request(server)
        .put('/api/admin/config')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updates);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('Updated Test Tenant');
      expect(response.body.data.theme).toBe('green');
      expect(response.body.data.primaryColor).toBe('#10b981');
    });

    it('should validate configuration updates', async () => {
      const invalidUpdates = {
        name: 123, // Should be string
        theme: 'invalid-theme'
      };

      const response = await request(server)
        .put('/api/admin/config')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidUpdates);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    it('should fail for non-admin user', async () => {
      const response = await request(server)
        .put('/api/admin/config')
        .set('Authorization', `Bearer ${userToken}`)
        .send({ name: 'Test' });

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Cross-tenant isolation', () => {
    beforeEach(async () => {
      // Create user in different tenant
      await User.create({
        customerId: 'other-tenant',
        email: '<EMAIL>',
        password: 'Password123!',
        role: UserRole.USER,
        profile: {
          firstName: 'Other',
          lastName: 'User'
        }
      });
    });

    it('should not return screens for different tenant', async () => {
      const loginResponse = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
          customerId: 'other-tenant'
        });

      const otherToken = loginResponse.body.data.tokens.accessToken;

      const response = await request(server)
        .get('/api/me/screens')
        .set('Authorization', `Bearer ${otherToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Tenant configuration not found');
    });
  });

  describe('Registry file updates', () => {
    it('should reflect registry file changes', async () => {
      // Update registry file
      const updatedRegistry = {
        ...mockRegistry,
        tenants: {
          ...mockRegistry.tenants,
          'test-tenant': {
            ...mockRegistry.tenants['test-tenant'],
            name: 'File Updated Tenant'
          }
        }
      };

      const registryPath = path.join(process.cwd(), 'registry.json');
      await fs.writeFile(registryPath, JSON.stringify(updatedRegistry, null, 2));

      // Wait a bit for file system changes
      await new Promise(resolve => setTimeout(resolve, 100));

      const response = await request(server)
        .get('/api/me/screens')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.tenant.name).toBe('File Updated Tenant');
    });
  });
});
