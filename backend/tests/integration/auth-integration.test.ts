import request from 'supertest';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import app from '../../src/app';
import User from '../../src/models/User';
import { UserRole } from '../../src/types';

describe('Authentication Integration Tests', () => {
  let mongoServer: MongoMemoryServer;
  let server: any;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    process.env.MONGODB_URI = mongoUri;
    
    // Import app after setting environment
    const { default: application } = await import('../../src/app');
    server = application;
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    await User.deleteMany({});
  });

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      // Create test user
      await User.create({
        customerId: 'test-tenant',
        email: '<EMAIL>',
        password: 'Password123!',
        role: UserRole.USER,
        profile: {
          firstName: 'Test',
          lastName: 'User'
        }
      });
    });

    it('should login successfully with valid credentials', async () => {
      const response = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
          customerId: 'test-tenant'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe('<EMAIL>');
      expect(response.body.data.tokens.accessToken).toBeDefined();
      expect(response.body.data.tokens.refreshToken).toBeDefined();
    });

    it('should fail with invalid credentials', async () => {
      const response = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword',
          customerId: 'test-tenant'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid credentials');
    });

    it('should fail with wrong tenant', async () => {
      const response = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
          customerId: 'wrong-tenant'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid credentials');
    });

    it('should fail with inactive user', async () => {
      // Deactivate user
      await User.updateOne(
        { email: '<EMAIL>' },
        { isActive: false }
      );

      const response = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
          customerId: 'test-tenant'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Account is deactivated');
    });

    it('should respect rate limiting', async () => {
      // Make multiple failed login attempts
      const promises = Array(6).fill(null).map(() =>
        request(server)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'wrongpassword',
            customerId: 'test-tenant'
          })
      );

      const responses = await Promise.all(promises);
      
      // Last request should be rate limited
      const lastResponse = responses[responses.length - 1];
      expect(lastResponse.status).toBe(429);
    });
  });

  describe('POST /api/auth/refresh', () => {
    let refreshToken: string;

    beforeEach(async () => {
      // Create user and login to get refresh token
      await User.create({
        customerId: 'test-tenant',
        email: '<EMAIL>',
        password: 'Password123!',
        role: UserRole.USER,
        profile: {
          firstName: 'Test',
          lastName: 'User'
        }
      });

      const loginResponse = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
          customerId: 'test-tenant'
        });

      refreshToken = loginResponse.body.data.tokens.refreshToken;
    });

    it('should refresh token successfully', async () => {
      const response = await request(server)
        .post('/api/auth/refresh')
        .send({ refreshToken });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.tokens.accessToken).toBeDefined();
      expect(response.body.data.tokens.refreshToken).toBeDefined();
    });

    it('should fail with invalid refresh token', async () => {
      const response = await request(server)
        .post('/api/auth/refresh')
        .send({ refreshToken: 'invalid-token' });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    it('should fail with missing refresh token', async () => {
      const response = await request(server)
        .post('/api/auth/refresh')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Refresh token required');
    });
  });

  describe('POST /api/auth/logout', () => {
    let accessToken: string;
    let refreshToken: string;

    beforeEach(async () => {
      // Create user and login
      await User.create({
        customerId: 'test-tenant',
        email: '<EMAIL>',
        password: 'Password123!',
        role: UserRole.USER,
        profile: {
          firstName: 'Test',
          lastName: 'User'
        }
      });

      const loginResponse = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
          customerId: 'test-tenant'
        });

      accessToken = loginResponse.body.data.tokens.accessToken;
      refreshToken = loginResponse.body.data.tokens.refreshToken;
    });

    it('should logout successfully', async () => {
      const response = await request(server)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ refreshToken });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Logged out successfully');
    });

    it('should fail without authentication', async () => {
      const response = await request(server)
        .post('/api/auth/logout')
        .send({ refreshToken });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    it('should fail without refresh token', async () => {
      const response = await request(server)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Refresh token required');
    });
  });

  describe('Tenant Isolation in Authentication', () => {
    beforeEach(async () => {
      // Create users in different tenants with same email
      await User.create({
        customerId: 'tenant1',
        email: '<EMAIL>',
        password: 'Password123!',
        role: UserRole.USER,
        profile: {
          firstName: 'User',
          lastName: 'One'
        }
      });

      await User.create({
        customerId: 'tenant2',
        email: '<EMAIL>',
        password: 'Password123!',
        role: UserRole.ADMIN,
        profile: {
          firstName: 'User',
          lastName: 'Two'
        }
      });
    });

    it('should authenticate correct user based on tenant', async () => {
      // Login as tenant1 user
      const response1 = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
          customerId: 'tenant1'
        });

      expect(response1.status).toBe(200);
      expect(response1.body.data.user.role).toBe(UserRole.USER);
      expect(response1.body.data.user.customerId).toBe('tenant1');

      // Login as tenant2 user
      const response2 = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
          customerId: 'tenant2'
        });

      expect(response2.status).toBe(200);
      expect(response2.body.data.user.role).toBe(UserRole.ADMIN);
      expect(response2.body.data.user.customerId).toBe('tenant2');
    });

    it('should not allow cross-tenant token usage', async () => {
      // Login as tenant1 user
      const loginResponse = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
          customerId: 'tenant1'
        });

      const accessToken = loginResponse.body.data.tokens.accessToken;

      // Try to use token to access tenant2 resources
      // This would be tested in the actual API endpoints
      // For now, we verify the token contains correct tenant info
      const jwt = require('jsonwebtoken');
      const decoded = jwt.decode(accessToken);
      expect(decoded.customerId).toBe('tenant1');
    });
  });
});
