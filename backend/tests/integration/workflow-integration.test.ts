import request from 'supertest';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import app from '../../src/app';
import User from '../../src/models/User';
import WorkflowExecution, { WorkflowExecutionStatus } from '../../src/models/WorkflowExecution';
import { UserRole } from '../../src/types';

// Mock n8n service
jest.mock('../../src/services/n8nService', () => ({
  n8nService: {
    triggerTicketCreated: jest.fn().mockResolvedValue({
      executionId: 'mock-exec-123',
      status: 'running',
      startedAt: new Date().toISOString()
    }),
    triggerTicketUpdated: jest.fn().mockResolvedValue({
      executionId: 'mock-exec-124',
      status: 'running',
      startedAt: new Date().toISOString()
    }),
    triggerCustomWebhook: jest.fn().mockResolvedValue({
      executionId: 'mock-exec-125',
      status: 'running'
    }),
    healthCheck: jest.fn().mockResolvedValue(true),
    listWorkflows: jest.fn().mockResolvedValue({
      data: [
        { id: 'workflow1', name: 'Test Workflow 1' },
        { id: 'workflow2', name: 'Test Workflow 2' }
      ]
    })
  },
  WORKFLOW_IDS: {
    TICKET_CREATED: 'ticket-created-workflow',
    TICKET_UPDATED: 'ticket-updated-workflow'
  }
}));

describe('Workflow Integration Tests', () => {
  let mongoServer: MongoMemoryServer;
  let server: any;
  let adminToken: string;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    process.env.MONGODB_URI = mongoUri;
    
    // Import app after setting environment
    const { default: application } = await import('../../src/app');
    server = application;
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    await User.deleteMany({});
    await WorkflowExecution.deleteMany({});

    // Create admin user
    const admin = await User.create({
      customerId: 'test-tenant',
      email: '<EMAIL>',
      password: 'Password123!',
      role: UserRole.ADMIN,
      profile: {
        firstName: 'Test',
        lastName: 'Admin'
      }
    });

    // Get admin token
    const adminLoginResponse = await request(server)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'Password123!',
        customerId: 'test-tenant'
      });

    adminToken = adminLoginResponse.body.data.tokens.accessToken;
  });

  describe('GET /api/workflows/executions', () => {
    beforeEach(async () => {
      // Create test workflow executions
      await WorkflowExecution.create({
        customerId: 'test-tenant',
        workflowId: 'test-workflow-1',
        workflowName: 'Test Workflow 1',
        executionId: 'exec-1',
        status: WorkflowExecutionStatus.SUCCESS,
        triggerType: 'api',
        triggerData: { test: 'data1' },
        result: { success: true },
        startedAt: new Date(),
        finishedAt: new Date(),
        duration: 5000
      });

      await WorkflowExecution.create({
        customerId: 'test-tenant',
        workflowId: 'test-workflow-2',
        workflowName: 'Test Workflow 2',
        executionId: 'exec-2',
        status: WorkflowExecutionStatus.ERROR,
        triggerType: 'webhook',
        triggerData: { test: 'data2' },
        error: 'Test error',
        startedAt: new Date(),
        finishedAt: new Date(),
        duration: 3000
      });
    });

    it('should get workflow executions for admin', async () => {
      const response = await request(server)
        .get('/api/workflows/executions')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination).toBeDefined();
      expect(response.body.pagination.total).toBe(2);
    });

    it('should filter executions by status', async () => {
      const response = await request(server)
        .get('/api/workflows/executions?status=success')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].status).toBe('success');
    });

    it('should filter executions by workflow ID', async () => {
      const response = await request(server)
        .get('/api/workflows/executions?workflowId=test-workflow-1')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].workflowId).toBe('test-workflow-1');
    });

    it('should support pagination', async () => {
      const response = await request(server)
        .get('/api/workflows/executions?page=1&limit=1')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(1);
      expect(response.body.pagination.pages).toBe(2);
    });

    it('should fail for non-admin users', async () => {
      // Create regular user
      const user = await User.create({
        customerId: 'test-tenant',
        email: '<EMAIL>',
        password: 'Password123!',
        role: UserRole.USER,
        profile: {
          firstName: 'Test',
          lastName: 'User'
        }
      });

      const userLoginResponse = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
          customerId: 'test-tenant'
        });

      const userToken = userLoginResponse.body.data.tokens.accessToken;

      const response = await request(server)
        .get('/api/workflows/executions')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/workflows/executions/:executionId', () => {
    let execution: any;

    beforeEach(async () => {
      execution = await WorkflowExecution.create({
        customerId: 'test-tenant',
        workflowId: 'test-workflow',
        workflowName: 'Test Workflow',
        executionId: 'exec-detail-test',
        status: WorkflowExecutionStatus.SUCCESS,
        triggerType: 'api',
        triggerData: { test: 'data' },
        result: { success: true },
        startedAt: new Date(),
        finishedAt: new Date()
      });
    });

    it('should get workflow execution by ID', async () => {
      const response = await request(server)
        .get(`/api/workflows/executions/${execution.executionId}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.executionId).toBe(execution.executionId);
      expect(response.body.data.workflowId).toBe('test-workflow');
    });

    it('should return 404 for non-existent execution', async () => {
      const response = await request(server)
        .get('/api/workflows/executions/non-existent')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Workflow execution not found');
    });
  });

  describe('POST /api/workflows/trigger', () => {
    it('should trigger workflow manually', async () => {
      const response = await request(server)
        .post('/api/workflows/trigger')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          workflowId: 'test-workflow',
          data: {
            testParam: 'testValue'
          }
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.execution).toBeDefined();
      expect(response.body.data.result).toBeDefined();
      expect(response.body.message).toBe('Workflow triggered successfully');
    });

    it('should fail without workflow ID', async () => {
      const response = await request(server)
        .post('/api/workflows/trigger')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          data: { test: 'data' }
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Workflow ID is required');
    });
  });

  describe('POST /api/workflows/executions/:executionId/retry', () => {
    let failedExecution: any;

    beforeEach(async () => {
      failedExecution = await WorkflowExecution.create({
        customerId: 'test-tenant',
        workflowId: 'test-workflow',
        workflowName: 'Test Workflow',
        executionId: 'exec-retry-test',
        status: WorkflowExecutionStatus.ERROR,
        triggerType: 'api',
        triggerData: { test: 'data' },
        err: 'Test error',
        retryCount: 0,
        maxRetries: 3,
        startedAt: new Date(),
        finishedAt: new Date()
      });
    });

    it('should retry failed workflow execution', async () => {
      const response = await request(server)
        .post(`/api/workflows/executions/${failedExecution.executionId}/retry`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Workflow execution retried successfully');

      // Check that retry count was incremented
      const updatedExecution = await WorkflowExecution.findOne({
        executionId: failedExecution.executionId
      });
      expect(updatedExecution?.retryCount).toBe(1);
    });

    it('should fail to retry execution that has reached max retries', async () => {
      // Update execution to max retries
      await WorkflowExecution.updateOne(
        { executionId: failedExecution.executionId },
        { retryCount: 3 }
      );

      const response = await request(server)
        .post(`/api/workflows/executions/${failedExecution.executionId}/retry`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Workflow execution cannot be retried');
    });
  });

  describe('GET /api/workflows/stats', () => {
    beforeEach(async () => {
      // Create various workflow executions for stats
      await WorkflowExecution.create([
        {
          customerId: 'test-tenant',
          workflowId: 'workflow-1',
          workflowName: 'Workflow 1',
          executionId: 'exec-stats-1',
          status: WorkflowExecutionStatus.SUCCESS,
          triggerType: 'api',
          triggerData: {},
          duration: 5000,
          startedAt: new Date(),
          finishedAt: new Date()
        },
        {
          customerId: 'test-tenant',
          workflowId: 'workflow-1',
          workflowName: 'Workflow 1',
          executionId: 'exec-stats-2',
          status: WorkflowExecutionStatus.ERROR,
          triggerType: 'webhook',
          triggerData: {},
          duration: 3000,
          startedAt: new Date(),
          finishedAt: new Date()
        },
        {
          customerId: 'test-tenant',
          workflowId: 'workflow-2',
          workflowName: 'Workflow 2',
          executionId: 'exec-stats-3',
          status: WorkflowExecutionStatus.SUCCESS,
          triggerType: 'api',
          triggerData: {},
          duration: 7000,
          startedAt: new Date(),
          finishedAt: new Date()
        }
      ]);
    });

    it('should get workflow statistics', async () => {
      const response = await request(server)
        .get('/api/workflows/stats')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.executionStats).toBeDefined();
      expect(response.body.data.workflowStats).toBeDefined();
      expect(response.body.data.recentExecutions).toBeDefined();
      expect(response.body.data.failedExecutions).toBeDefined();
    });
  });

  describe('GET /api/workflows/test-connection', () => {
    it('should test n8n connection successfully', async () => {
      const response = await request(server)
        .get('/api/workflows/test-connection')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.connected).toBe(true);
      expect(response.body.data.timestamp).toBeDefined();
    });
  });

  describe('Cross-tenant isolation', () => {
    beforeEach(async () => {
      // Create execution for different tenant
      await WorkflowExecution.create({
        customerId: 'other-tenant',
        workflowId: 'other-workflow',
        workflowName: 'Other Workflow',
        executionId: 'other-exec',
        status: WorkflowExecutionStatus.SUCCESS,
        triggerType: 'api',
        triggerData: {},
        startedAt: new Date(),
        finishedAt: new Date()
      });
    });

    it('should not return executions from other tenants', async () => {
      const response = await request(server)
        .get('/api/workflows/executions')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(0);
    });
  });
});
