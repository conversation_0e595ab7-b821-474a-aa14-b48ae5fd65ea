import fs from 'fs/promises';
import path from 'path';
import { 
  getTenantConfig, 
  getTenantScreens, 
  hasScreenAccess, 
  updateTenantConfig,
  addTenantScreen,
  updateTenantScreen,
  removeTenantScreen,
  validateTenantConfig
} from '../../src/services/registryService';
import { UserRole, TenantConfig, ScreenConfig } from '../../src/types';

// Mock fs module
jest.mock('fs/promises');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('Registry Service', () => {
  const mockRegistry = {
    tenants: {
      'test-tenant': {
        name: 'Test Tenant',
        theme: 'blue',
        primaryColor: '#3b82f6',
        secondaryColor: '#1e40af',
        logo: 'https://example.com/logo.png',
        favicon: '/favicon.ico',
        screens: [
          {
            id: 'support-tickets',
            name: 'Support Tickets',
            url: '/support-tickets',
            icon: 'ticket',
            permissions: [UserRole.USER, UserRole.ADMIN],
            badge: { enabled: true, color: 'red' }
          },
          {
            id: 'admin-dashboard',
            name: 'Admin Dashboard',
            url: '/admin',
            icon: 'dashboard',
            permissions: [UserRole.ADMIN],
            badge: { enabled: false }
          }
        ],
        features: {
          realTimeNotifications: true,
          auditLogging: true
        },
        settings: {
          timezone: 'UTC',
          language: 'en'
        }
      }
    },
    globalSettings: {
      maxUsersPerTenant: 100
    },
    version: '1.0.0',
    lastUpdated: '2024-01-01T00:00:00Z'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock file system operations
    mockFs.readFile.mockResolvedValue(JSON.stringify(mockRegistry));
    mockFs.writeFile.mockResolvedValue();
    mockFs.stat.mockResolvedValue({
      mtimeMs: Date.now()
    } as any);
  });

  describe('getTenantConfig', () => {
    it('should return tenant configuration', async () => {
      const config = await getTenantConfig('test-tenant');
      
      expect(config).toBeDefined();
      expect(config?.name).toBe('Test Tenant');
      expect(config?.theme).toBe('blue');
      expect(config?.screens).toHaveLength(2);
    });

    it('should return null for non-existent tenant', async () => {
      const config = await getTenantConfig('non-existent');
      expect(config).toBeNull();
    });
  });

  describe('getTenantScreens', () => {
    it('should return screens filtered by user role', async () => {
      const userScreens = await getTenantScreens('test-tenant', UserRole.USER);
      expect(userScreens).toHaveLength(1);
      expect(userScreens[0].id).toBe('support-tickets');

      const adminScreens = await getTenantScreens('test-tenant', UserRole.ADMIN);
      expect(adminScreens).toHaveLength(2);
    });

    it('should return empty array for non-existent tenant', async () => {
      const screens = await getTenantScreens('non-existent', UserRole.USER);
      expect(screens).toEqual([]);
    });
  });

  describe('hasScreenAccess', () => {
    it('should return true for authorized access', async () => {
      const hasAccess = await hasScreenAccess('test-tenant', 'support-tickets', UserRole.USER);
      expect(hasAccess).toBe(true);
    });

    it('should return false for unauthorized access', async () => {
      const hasAccess = await hasScreenAccess('test-tenant', 'admin-dashboard', UserRole.USER);
      expect(hasAccess).toBe(false);
    });

    it('should return false for non-existent screen', async () => {
      const hasAccess = await hasScreenAccess('test-tenant', 'non-existent', UserRole.USER);
      expect(hasAccess).toBe(false);
    });
  });

  describe('updateTenantConfig', () => {
    it('should update tenant configuration', async () => {
      const updates = {
        name: 'Updated Tenant Name',
        theme: 'green'
      };

      const updatedConfig = await updateTenantConfig('test-tenant', updates);
      
      expect(updatedConfig.name).toBe('Updated Tenant Name');
      expect(updatedConfig.theme).toBe('green');
      expect(mockFs.writeFile).toHaveBeenCalled();
    });

    it('should throw error for non-existent tenant', async () => {
      await expect(updateTenantConfig('non-existent', { name: 'Test' }))
        .rejects.toThrow('Tenant not found');
    });
  });

  describe('addTenantScreen', () => {
    it('should add new screen to tenant', async () => {
      const newScreen: ScreenConfig = {
        id: 'new-screen',
        name: 'New Screen',
        url: '/new-screen',
        icon: 'new',
        permissions: [UserRole.USER],
        badge: { enabled: false }
      };

      const screens = await addTenantScreen('test-tenant', newScreen);
      
      expect(screens).toHaveLength(3);
      expect(screens.find(s => s.id === 'new-screen')).toBeDefined();
      expect(mockFs.writeFile).toHaveBeenCalled();
    });

    it('should throw error for duplicate screen ID', async () => {
      const duplicateScreen: ScreenConfig = {
        id: 'support-tickets', // Already exists
        name: 'Duplicate Screen',
        url: '/duplicate',
        icon: 'duplicate',
        permissions: [UserRole.USER]
      };

      await expect(addTenantScreen('test-tenant', duplicateScreen))
        .rejects.toThrow('Screen ID already exists');
    });
  });

  describe('updateTenantScreen', () => {
    it('should update existing screen', async () => {
      const updates = {
        name: 'Updated Support Tickets',
        icon: 'updated-ticket'
      };

      const updatedScreen = await updateTenantScreen('test-tenant', 'support-tickets', updates);
      
      expect(updatedScreen?.name).toBe('Updated Support Tickets');
      expect(updatedScreen?.icon).toBe('updated-ticket');
      expect(mockFs.writeFile).toHaveBeenCalled();
    });

    it('should throw error for non-existent screen', async () => {
      await expect(updateTenantScreen('test-tenant', 'non-existent', { name: 'Test' }))
        .rejects.toThrow('Screen not found');
    });
  });

  describe('removeTenantScreen', () => {
    it('should remove screen from tenant', async () => {
      const result = await removeTenantScreen('test-tenant', 'support-tickets');
      
      expect(result).toBe(true);
      expect(mockFs.writeFile).toHaveBeenCalled();
    });

    it('should return false for non-existent screen', async () => {
      const result = await removeTenantScreen('test-tenant', 'non-existent');
      expect(result).toBe(false);
    });
  });

  describe('validateTenantConfig', () => {
    it('should return no errors for valid config', () => {
      const validConfig: Partial<TenantConfig> = {
        name: 'Valid Tenant',
        theme: 'blue',
        screens: [
          {
            id: 'valid-screen',
            name: 'Valid Screen',
            url: '/valid',
            icon: 'valid',
            permissions: [UserRole.USER]
          }
        ]
      };

      const errors = validateTenantConfig(validConfig);
      expect(errors).toHaveLength(0);
    });

    it('should return errors for invalid config', () => {
      const invalidConfig: any = {
        name: 123, // Should be string
        theme: 'invalid-theme', // Invalid theme
        screens: [
          {
            // Missing required fields
            name: 'Invalid Screen'
          }
        ]
      };

      const errors = validateTenantConfig(invalidConfig);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors).toContain('Name must be a string');
      expect(errors).toContain('Theme must be one of: blue, green, red, purple, orange');
    });

    it('should validate screen permissions', () => {
      const configWithInvalidPermissions: any = {
        screens: [
          {
            id: 'test-screen',
            name: 'Test Screen',
            url: '/test',
            icon: 'test',
            permissions: 'invalid' // Should be array
          }
        ]
      };

      const errors = validateTenantConfig(configWithInvalidPermissions);
      expect(errors).toContain('Screen 0: Permissions are required and must be an array');
    });
  });

  describe('File System Error Handling', () => {
    it('should handle file read errors', async () => {
      mockFs.readFile.mockRejectedValue(new Error('File not found'));

      await expect(getTenantConfig('test-tenant'))
        .rejects.toThrow('Failed to load tenant registry');
    });

    it('should handle file write errors', async () => {
      mockFs.writeFile.mockRejectedValue(new Error('Permission denied'));

      await expect(updateTenantConfig('test-tenant', { name: 'Test' }))
        .rejects.toThrow('Failed to save tenant registry');
    });
  });

  describe('Caching', () => {
    it('should cache registry data', async () => {
      // First call
      await getTenantConfig('test-tenant');
      expect(mockFs.readFile).toHaveBeenCalledTimes(1);
      expect(mockFs.stat).toHaveBeenCalledTimes(1);

      // Second call should use cache if file hasn't changed
      mockFs.stat.mockResolvedValue({
        mtimeMs: 0 // Older than cache
      } as any);

      await getTenantConfig('test-tenant');
      expect(mockFs.readFile).toHaveBeenCalledTimes(1); // Still only 1 call
      expect(mockFs.stat).toHaveBeenCalledTimes(2); // But stat is called again
    });

    it('should reload registry when file is modified', async () => {
      // First call
      await getTenantConfig('test-tenant');
      expect(mockFs.readFile).toHaveBeenCalledTimes(1);

      // Second call with newer file
      mockFs.stat.mockResolvedValue({
        mtimeMs: Date.now() + 1000 // Newer than cache
      } as any);

      await getTenantConfig('test-tenant');
      expect(mockFs.readFile).toHaveBeenCalledTimes(2); // File read again
    });
  });
});
