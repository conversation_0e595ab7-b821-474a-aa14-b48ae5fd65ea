import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import User from '../../src/models/User';
import Ticket from '../../src/models/Ticket';
import AuditLog from '../../src/models/AuditLog';
import { 
  setCurrentTenant, 
  clearCurrentTenant, 
  withTenant,
  validateTenantAccess,
  getTenantStats,
  cleanupTenantData,
  exportTenantData
} from '../../src/middleware/tenant';
import { UserRole, TicketStatus, TicketPriority, AuditAction } from '../../src/types';

describe('Tenant Isolation System', () => {
  let mongoServer: MongoMemoryServer;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear all collections
    await User.deleteMany({});
    await Ticket.deleteMany({});
    await AuditLog.deleteMany({});
    clearCurrentTenant();
  });

  describe('User Model Tenant Isolation', () => {
    it('should automatically filter users by tenant', async () => {
      // Create users for different tenants
      const user1 = await User.create({
        customerId: 'tenant1',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'One' }
      });

      const user2 = await User.create({
        customerId: 'tenant2',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'Two' }
      });

      // Set tenant context and query
      setCurrentTenant('tenant1');
      const tenant1Users = await User.find({});
      expect(tenant1Users).toHaveLength(1);
      expect(tenant1Users[0].email).toBe('<EMAIL>');

      // Switch tenant context
      setCurrentTenant('tenant2');
      const tenant2Users = await User.find({});
      expect(tenant2Users).toHaveLength(1);
      expect(tenant2Users[0].email).toBe('<EMAIL>');
    });

    it('should prevent cross-tenant user access', async () => {
      const user1 = await User.create({
        customerId: 'tenant1',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'One' }
      });

      // Try to access user from different tenant
      setCurrentTenant('tenant2');
      const foundUser = await User.findById(user1._id);
      expect(foundUser).toBeNull();
    });

    it('should allow admin to access all tenants when filter is disabled', async () => {
      const user1 = await User.create({
        customerId: 'tenant1',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'One' }
      });

      const user2 = await User.create({
        customerId: 'tenant2',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'Two' }
      });

      // Access all users without tenant filter
      const allUsers = await User.findAcrossAllTenants();
      expect(allUsers).toHaveLength(2);
    });

    it('should enforce unique email per tenant', async () => {
      // Create user in tenant1
      await User.create({
        customerId: 'tenant1',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'One' }
      });

      // Should allow same email in different tenant
      const user2 = await User.create({
        customerId: 'tenant2',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'Two' }
      });

      expect(user2).toBeDefined();

      // Should prevent duplicate email in same tenant
      await expect(User.create({
        customerId: 'tenant1',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'Three' }
      })).rejects.toThrow();
    });
  });

  describe('Ticket Model Tenant Isolation', () => {
    let user1: any, user2: any;

    beforeEach(async () => {
      user1 = await User.create({
        customerId: 'tenant1',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'One' }
      });

      user2 = await User.create({
        customerId: 'tenant2',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'Two' }
      });
    });

    it('should automatically filter tickets by tenant', async () => {
      const ticket1 = await Ticket.create({
        customerId: 'tenant1',
        userId: user1._id,
        title: 'Ticket 1',
        description: 'Description 1',
        status: TicketStatus.OPEN,
        priority: TicketPriority.MEDIUM,
        category: 'Support'
      });

      const ticket2 = await Ticket.create({
        customerId: 'tenant2',
        userId: user2._id,
        title: 'Ticket 2',
        description: 'Description 2',
        status: TicketStatus.OPEN,
        priority: TicketPriority.HIGH,
        category: 'Bug'
      });

      // Set tenant context and query
      setCurrentTenant('tenant1');
      const tenant1Tickets = await Ticket.find({});
      expect(tenant1Tickets).toHaveLength(1);
      expect(tenant1Tickets[0].title).toBe('Ticket 1');

      // Switch tenant context
      setCurrentTenant('tenant2');
      const tenant2Tickets = await Ticket.find({});
      expect(tenant2Tickets).toHaveLength(1);
      expect(tenant2Tickets[0].title).toBe('Ticket 2');
    });

    it('should prevent cross-tenant ticket updates', async () => {
      const ticket = await Ticket.create({
        customerId: 'tenant1',
        userId: user1._id,
        title: 'Original Title',
        description: 'Original Description',
        status: TicketStatus.OPEN,
        priority: TicketPriority.MEDIUM,
        category: 'Support'
      });

      // Try to update from different tenant
      setCurrentTenant('tenant2');
      const result = await Ticket.updateOne(
        { _id: ticket._id },
        { title: 'Updated Title' }
      );

      expect(result.matchedCount).toBe(0);
      expect(result.modifiedCount).toBe(0);
    });

    it('should prevent cross-tenant ticket deletion', async () => {
      const ticket = await Ticket.create({
        customerId: 'tenant1',
        userId: user1._id,
        title: 'To Delete',
        description: 'Description',
        status: TicketStatus.OPEN,
        priority: TicketPriority.LOW,
        category: 'Support'
      });

      // Try to delete from different tenant
      setCurrentTenant('tenant2');
      const result = await Ticket.deleteOne({ _id: ticket._id });
      expect(result.deletedCount).toBe(0);

      // Verify ticket still exists in original tenant
      setCurrentTenant('tenant1');
      const foundTicket = await Ticket.findById(ticket._id);
      expect(foundTicket).toBeDefined();
    });
  });

  describe('Audit Log Tenant Isolation', () => {
    let user1: any, user2: any;

    beforeEach(async () => {
      user1 = await User.create({
        customerId: 'tenant1',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'One' }
      });

      user2 = await User.create({
        customerId: 'tenant2',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'Two' }
      });
    });

    it('should automatically filter audit logs by tenant', async () => {
      await AuditLog.logAction(
        'tenant1',
        user1._id.toString(),
        AuditAction.LOGIN,
        'User',
        { email: user1.email },
        user1._id.toString(),
        '127.0.0.1',
        'test-agent'
      );

      await AuditLog.logAction(
        'tenant2',
        user2._id.toString(),
        AuditAction.LOGIN,
        'User',
        { email: user2.email },
        user2._id.toString(),
        '127.0.0.1',
        'test-agent'
      );

      // Set tenant context and query
      setCurrentTenant('tenant1');
      const tenant1Logs = await AuditLog.find({});
      expect(tenant1Logs).toHaveLength(1);
      expect(tenant1Logs[0].details.email).toBe(user1.email);

      // Switch tenant context
      setCurrentTenant('tenant2');
      const tenant2Logs = await AuditLog.find({});
      expect(tenant2Logs).toHaveLength(1);
      expect(tenant2Logs[0].details.email).toBe(user2.email);
    });
  });

  describe('Tenant Context Management', () => {
    it('should execute function with tenant context', async () => {
      const user1 = await User.create({
        customerId: 'tenant1',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'One' }
      });

      const user2 = await User.create({
        customerId: 'tenant2',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'Two' }
      });

      const result = await withTenant('tenant1', async () => {
        const users = await User.find({});
        return users.length;
      });

      expect(result).toBe(1);
    });

    it('should restore previous tenant context after execution', async () => {
      setCurrentTenant('tenant1');
      
      await withTenant('tenant2', async () => {
        // Inside this block, tenant should be 'tenant2'
        // But we don't need to test anything here
      });

      // After execution, should be back to 'tenant1'
      expect(getCurrentTenant()).toBe('tenant1');
    });
  });

  describe('Tenant Utilities', () => {
    let user1: any, user2: any, ticket1: any;

    beforeEach(async () => {
      user1 = await User.create({
        customerId: 'tenant1',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'One' }
      });

      user2 = await User.create({
        customerId: 'tenant2',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER,
        profile: { firstName: 'User', lastName: 'Two' }
      });

      ticket1 = await Ticket.create({
        customerId: 'tenant1',
        userId: user1._id,
        title: 'Test Ticket',
        description: 'Test Description',
        status: TicketStatus.OPEN,
        priority: TicketPriority.MEDIUM,
        category: 'Support'
      });
    });

    it('should validate tenant access correctly', async () => {
      const hasAccess = await validateTenantAccess(Ticket, ticket1._id.toString(), 'tenant1');
      expect(hasAccess).toBe(true);

      const noAccess = await validateTenantAccess(Ticket, ticket1._id.toString(), 'tenant2');
      expect(noAccess).toBe(false);
    });

    it('should get tenant statistics', async () => {
      const stats = await getTenantStats('tenant1');
      expect(stats.users).toBe(1);
      expect(stats.tickets).toBe(1);
      expect(stats.auditLogs).toBe(0);
    });

    it('should export tenant data', async () => {
      const exportData = await exportTenantData('tenant1');
      expect(exportData.customerId).toBe('tenant1');
      expect(exportData.data.users).toHaveLength(1);
      expect(exportData.data.tickets).toHaveLength(1);
      expect(exportData.data.auditLogs).toHaveLength(0);
    });

    it('should cleanup tenant data', async () => {
      await cleanupTenantData('tenant1');
      
      // Verify data is soft deleted
      const users = await User.find({ customerId: 'tenant1' }, null, { skipTenantFilter: true });
      const tickets = await Ticket.find({ customerId: 'tenant1' }, null, { skipTenantFilter: true });
      
      expect(users[0].deletedAt).toBeDefined();
      expect(tickets[0].deletedAt).toBeDefined();
    });
  });
});

// Helper function to get current tenant (for testing)
function getCurrentTenant() {
  // This would normally be imported from the tenant middleware
  // For testing purposes, we'll access the internal state
  return require('../../src/middleware/tenant').getCurrentTenant();
}
