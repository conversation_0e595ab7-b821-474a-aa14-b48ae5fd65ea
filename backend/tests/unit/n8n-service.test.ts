import axios from 'axios';
import { n8nService, WORKFLOW_IDS } from '../../src/services/n8nService';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock axios.create
const mockAxiosInstance = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  interceptors: {
    request: { use: jest.fn() },
    response: { use: jest.fn() }
  }
};

mockedAxios.create.mockReturnValue(mockAxiosInstance as any);
mockedAxios.mockImplementation(() => Promise.resolve({ data: {} }));

describe('N8n Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('triggerTicketCreated', () => {
    it('should trigger ticket created workflow successfully', async () => {
      const mockTicket = {
        _id: 'ticket123',
        title: 'Test Ticket',
        description: 'Test Description',
        status: 'Open',
        priority: 'Medium',
        category: 'Support',
        customerId: 'tenant1',
        createdAt: new Date().toISOString()
      };

      const mockUser = {
        _id: 'user123',
        email: '<EMAIL>',
        profile: {
          firstName: 'John',
          lastName: 'Doe'
        },
        role: 'User'
      };

      const mockResponse = {
        executionId: 'exec123',
        status: 'running',
        startedAt: new Date().toISOString()
      };

      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse });

      const result = await n8nService.triggerTicketCreated(mockTicket, mockUser);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        `/workflows/${WORKFLOW_IDS.TICKET_CREATED}/execute`,
        {
          data: {
            ticket: {
              id: mockTicket._id,
              title: mockTicket.title,
              description: mockTicket.description,
              status: mockTicket.status,
              priority: mockTicket.priority,
              category: mockTicket.category,
              customerId: mockTicket.customerId,
              createdAt: mockTicket.createdAt
            },
            user: {
              id: mockUser._id,
              email: mockUser.email,
              name: 'John Doe',
              role: mockUser.role
            },
            metadata: expect.objectContaining({
              source: 'flowbit-api'
            })
          }
        }
      );

      expect(result).toEqual({
        executionId: 'exec123',
        status: 'running',
        startedAt: expect.any(String)
      });
    });

    it('should handle workflow execution errors', async () => {
      const mockTicket = {
        _id: 'ticket123',
        title: 'Test Ticket',
        description: 'Test Description',
        status: 'Open',
        priority: 'Medium',
        category: 'Support',
        customerId: 'tenant1',
        createdAt: new Date().toISOString()
      };

      const mockUser = {
        _id: 'user123',
        email: '<EMAIL>',
        profile: {
          firstName: 'John',
          lastName: 'Doe'
        },
        role: 'User'
      };

      mockAxiosInstance.post.mockRejectedValue(new Error('Network error'));

      await expect(n8nService.triggerTicketCreated(mockTicket, mockUser))
        .rejects.toThrow('Failed to execute workflow ticket-created-workflow: Network error');
    });
  });

  describe('triggerTicketUpdated', () => {
    it('should trigger ticket updated workflow with changes', async () => {
      const mockTicket = {
        _id: 'ticket123',
        title: 'Updated Ticket',
        description: 'Updated Description',
        status: 'InProgress',
        priority: 'High',
        category: 'Support',
        customerId: 'tenant1',
        updatedAt: new Date().toISOString()
      };

      const mockUser = {
        _id: 'user123',
        email: '<EMAIL>',
        profile: {
          firstName: 'John',
          lastName: 'Doe'
        },
        role: 'User'
      };

      const changes = {
        status: { from: 'Open', to: 'InProgress' },
        priority: { from: 'Medium', to: 'High' }
      };

      const mockResponse = {
        executionId: 'exec124',
        status: 'running',
        startedAt: new Date().toISOString()
      };

      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse });

      const result = await n8nService.triggerTicketUpdated(mockTicket, mockUser, changes);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        `/workflows/${WORKFLOW_IDS.TICKET_UPDATED}/execute`,
        {
          data: {
            ticket: expect.objectContaining({
              id: mockTicket._id,
              status: mockTicket.status,
              priority: mockTicket.priority
            }),
            user: expect.objectContaining({
              id: mockUser._id,
              name: 'John Doe'
            }),
            changes,
            metadata: expect.objectContaining({
              source: 'flowbit-api'
            })
          }
        }
      );

      expect(result.executionId).toBe('exec124');
    });
  });

  describe('triggerTicketAssigned', () => {
    it('should trigger ticket assigned workflow', async () => {
      const mockTicket = {
        _id: 'ticket123',
        title: 'Test Ticket',
        description: 'Test Description',
        status: 'Open',
        priority: 'Medium',
        category: 'Support',
        customerId: 'tenant1'
      };

      const mockAssignedUser = {
        _id: 'user456',
        email: '<EMAIL>',
        profile: {
          firstName: 'Jane',
          lastName: 'Smith'
        },
        role: 'Admin'
      };

      const mockAssignedBy = {
        _id: 'user123',
        email: '<EMAIL>',
        profile: {
          firstName: 'John',
          lastName: 'Doe'
        },
        role: 'Admin'
      };

      const mockResponse = {
        executionId: 'exec125',
        status: 'running',
        startedAt: new Date().toISOString()
      };

      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse });

      const result = await n8nService.triggerTicketAssigned(mockTicket, mockAssignedUser, mockAssignedBy);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        `/workflows/${WORKFLOW_IDS.TICKET_ASSIGNED}/execute`,
        {
          data: {
            ticket: expect.objectContaining({
              id: mockTicket._id
            }),
            assignedUser: expect.objectContaining({
              id: mockAssignedUser._id,
              name: 'Jane Smith'
            }),
            assignedBy: expect.objectContaining({
              id: mockAssignedBy._id,
              name: 'John Doe'
            }),
            metadata: expect.objectContaining({
              source: 'flowbit-api'
            })
          }
        }
      );

      expect(result.executionId).toBe('exec125');
    });
  });

  describe('triggerCustomWebhook', () => {
    it('should trigger custom webhook with POST method', async () => {
      const webhookPath = 'custom-workflow';
      const data = { customData: 'test' };
      const mockResponse = { success: true };

      mockedAxios.mockResolvedValue({ data: mockResponse });

      const result = await n8nService.triggerCustomWebhook(webhookPath, data, 'POST');

      expect(mockedAxios).toHaveBeenCalledWith({
        method: 'POST',
        url: `http://localhost:5678/webhook/${webhookPath}`,
        headers: {
          'Content-Type': 'application/json',
          'X-Webhook-Secret': 'n8n-webhook-secret-key'
        },
        timeout: 30000,
        data
      });

      expect(result).toEqual(mockResponse);
    });

    it('should trigger custom webhook with GET method', async () => {
      const webhookPath = 'status-check';
      const data = { status: 'check' };
      const mockResponse = { status: 'healthy' };

      mockedAxios.mockResolvedValue({ data: mockResponse });

      const result = await n8nService.triggerCustomWebhook(webhookPath, data, 'GET');

      expect(mockedAxios).toHaveBeenCalledWith({
        method: 'GET',
        url: `http://localhost:5678/webhook/${webhookPath}`,
        headers: {
          'Content-Type': 'application/json',
          'X-Webhook-Secret': 'n8n-webhook-secret-key'
        },
        timeout: 30000,
        params: data
      });

      expect(result).toEqual(mockResponse);
    });
  });

  describe('healthCheck', () => {
    it('should return true when n8n is healthy', async () => {
      mockAxiosInstance.get.mockResolvedValue({ data: { workflows: [] } });

      const result = await n8nService.healthCheck();

      expect(result).toBe(true);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/workflows');
    });

    it('should return false when n8n is unhealthy', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Connection failed'));

      const result = await n8nService.healthCheck();

      expect(result).toBe(false);
    });
  });

  describe('getExecutionStatus', () => {
    it('should get execution status successfully', async () => {
      const executionId = 'exec123';
      const mockResponse = {
        status: 'success',
        data: { result: 'completed' },
        startedAt: '2024-01-01T00:00:00Z',
        finishedAt: '2024-01-01T00:01:00Z'
      };

      mockAxiosInstance.get.mockResolvedValue({ data: mockResponse });

      const result = await n8nService.getExecutionStatus(executionId);

      expect(mockAxiosInstance.get).toHaveBeenCalledWith(`/executions/${executionId}`);
      expect(result).toEqual({
        executionId,
        status: 'success',
        data: { result: 'completed' },
        error: undefined,
        startedAt: '2024-01-01T00:00:00Z',
        finishedAt: '2024-01-01T00:01:00Z'
      });
    });
  });
});
