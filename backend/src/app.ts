import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import dotenv from 'dotenv';

// Import middleware
import { 
  apiRateLimit, 
  authRateLimit, 
  securityHeaders, 
  corsOptions, 
  requestSizeLimit, 
  requestTimeout, 
  requestLogger, 
  errorHandler 
} from '@/middleware/security';
import { 
  tenantIsolationMiddleware, 
  setTenantContext, 
  clearTenantContext 
} from '@/middleware/tenant';

// Import routes
import authRoutes from '@/routes/auth';
import userRoutes from '@/routes/user';
import ticketRoutes from '@/routes/ticket';
import adminRoutes from '@/routes/admin';
import webhookRoutes from '@/routes/webhook';
import healthRoutes from '@/routes/health';

// Import utilities
import { connectDatabase } from '@/utils/database';

// Load environment variables
dotenv.config();

// Create Express app
const app = express();
const server = createServer(app);

// Initialize Socket.IO
const io = new SocketIOServer(server, {
  cors: corsOptions,
  transports: ['websocket', 'polling']
});

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Global middleware
app.use(securityHeaders);
app.use(cors(corsOptions));
app.use(compression());
app.use(requestSizeLimit);
app.use(requestTimeout);
app.use(requestLogger);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Tenant context middleware
app.use(setTenantContext);
app.use(clearTenantContext);

// Health check routes (no rate limiting)
app.use('/api/health', healthRoutes);

// Authentication routes with strict rate limiting
app.use('/api/auth', authRateLimit, authRoutes);

// Webhook routes (no rate limiting for external services)
app.use('/api/webhook', webhookRoutes);

// Protected routes with general rate limiting
app.use('/api', apiRateLimit);
app.use('/api/me', userRoutes);
app.use('/api/tickets', ticketRoutes);
app.use('/api/admin', adminRoutes);

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`Client connected: ${socket.id}`);

  // Handle authentication for socket connections
  socket.on('authenticate', async (data) => {
    try {
      const { token } = data;
      // Verify JWT token and set user context
      // Implementation would go here
      socket.emit('authenticated', { success: true });
    } catch (error) {
      socket.emit('authentication_error', { error: 'Invalid token' });
    }
  });

  // Handle ticket updates
  socket.on('join_ticket_room', (ticketId) => {
    socket.join(`ticket_${ticketId}`);
  });

  socket.on('leave_ticket_room', (ticketId) => {
    socket.leave(`ticket_${ticketId}`);
  });

  socket.on('disconnect', () => {
    console.log(`Client disconnected: ${socket.id}`);
  });
});

// Make io available to routes
app.set('io', io);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found'
  });
});

// Global error handler
app.use(errorHandler);

// Database connection and server startup
const startServer = async () => {
  try {
    // Connect to database
    await connectDatabase();

    // Start server
    const PORT = process.env.PORT || 3001;
    server.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔗 API URL: http://localhost:${PORT}/api`);
      console.log(`🏥 Health Check: http://localhost:${PORT}/api/health`);
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('SIGTERM received, shutting down gracefully');
      server.close(() => {
        console.log('Process terminated');
      });
    });

    process.on('SIGINT', () => {
      console.log('SIGINT received, shutting down gracefully');
      server.close(() => {
        console.log('Process terminated');
      });
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Start server only if not in test environment
if (process.env.NODE_ENV !== 'test') {
  startServer();
}

export default app;
