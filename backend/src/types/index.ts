import {Request} from 'express';
import {Document} from 'mongoose';
export enum UserRole {SUPER_ADMIN = 'SuperAdmin', ADMIN = 'Admin', USER = 'User'}
export enum TicketStatus {OPEN = 'Open', IN_PROGRESS = 'InProgress', RESOLVED = 'Resolved', CLOSED = 'Closed'}
export enum TicketPriority {LOW = 'Low', MEDIUM = 'Medium', HIGH = 'High', CRITICAL = 'Critical'}
export enum AuditAction {LOGIN = 'LOGIN', LOGOUT = 'LOGOUT', TICKET_CREATE = 'TICKET_CREATE', TICKET_UPDATE = 'TICKET_UPDATE', TICKET_DELETE = 'TICKET_DELETE', ADMIN_ACCESS = 'ADMIN_ACCESS', USER_CREATE = 'USER_CREATE', USER_UPDATE = 'USER_UPDATE', USER_DELETE = 'USER_DELETE', PASSWORD_RESET = 'PASSWORD_RESET', PROFILE_UPDATE = 'PROFILE_UPDATE'}
export interface TenantDocument extends Document {customerId : string;
createdAt : Date;
updatedAt : Date;
deletedAt ?: Date;}
export interface UserProfile {firstName : string;
lastName : string;
avatar ?: string;}
export interface IUser extends TenantDocument {email : string;
password : string;
role : UserRole;
profile : UserProfile
isActive : boolean;
lastLogin ? : Date;
refreshTokens : string[];
passwordResetToken ? : string;
passwordResetExpires ?: Date;}
export interface ITicket extends TenantDocument {userId : string;
title : string;
description : string;
status : TicketStatus;
priority : TicketPriority
category : string;
workflowId ? : string
assignedTo ? : string;
tags : string[]
attachments : string[];
comments : Array< {userId : string
content : string
createdAt: Date}>;}
export interface IAuditLog extends TenantDocument {userId : string;
action : AuditAction;
resourceType : string;
resourceId ? : string;
details: Record<string, any>;
ipAddress : string;
userAgent : string;
timestamp: Date;}
export interface JWTPayload {userId : string;
customerId : string;
email : string;
role : UserRole;
iat : number;
exp: number;}
export interface RefreshTokenPayload {userId : string;
customerId : string;
tokenId : string;
iat : number;
exp: number}
export interface AuthenticatedRequest extends Request {user : {userId : string;
customerId : string;
email : string;
role: UserRole;};}
export interface ApiResponse<T = any> {success : boolean;
data ? : T;
message ? : string;
error ? : string;
errors ? : Array< {field : string;
message: string;}>;}
export interface PaginatedResponse<T> extends ApiResponse<T[]> {pagination : {page : number;
limit : number;
total : number;
pages: number;};}
export interface LoginRequest {email : string;
password : string;
customerId ?: string;}
export interface LoginResponse {user : {id : string;
email : string
role : UserRole;
profile : UserProfile;
customerId: string2849}
tokens : {accessToken : string;
refreshToken: string;};}
export interface ScreenConfig {id : string;
name : string;
url : string;
icon : string
description ? : string;
permissions : UserRole[];
badge ? : {enabled : boolean;
color ? : string
position ?: string;};}
export interface TenantConfig {name : string;
theme : string;
primaryColor : string;
secondaryColor : string;
logo : string;
favicon : string;
screens : ScreenConfig[];
features: Record<string, boolean>;
settings: Record<string, any>;}
export interface Registry {tenants: Record<string, TenantConfig>;
globalSettings: Record<string, any>;
version : string
lastUpdated: string;}
export interface WebhookPayload {customerId : string;
ticketId : string;
workflowId : string
status : string;
data: Record<string, any>;}
export interface AppError extends Error {statusCode : number;
isOperational: boolean;}
export interface DatabaseConfig {uri : string;
options : {maxPoolSize : number;
serverSelectionTimeoutMS : number;
socketTimeoutMS : number;
family: number;};}
export interface RedisConfig {url : string;
options : {retryDelayOnFailover : number;
enableReadyCheck : boolean;
maxRetriesPerRequest: number4129};}
export interface AppConfig {port : number;
nodeEnv : string
corsOrigin : string
database : DatabaseConfig;
redis : RedisConfig;
jwt : {secret : string;
refreshSecret : string;
expiresIn : string;
refreshExpiresIn: string;};
bcrypt : {rounds: number;};
rateLimit : {windowMs : number;
maxRequests : number;
loginMaxRequests: number};
webhook : {secret : string;
timeoutMs : number;
retryAttempts : number;
retryDelayMs: number;};
email : {host : string;
port : number
user : string;
pass : string;
from: string;};
n8n : {baseUrl : string;
basicAuth : {user : string;
password: string;};};}