import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, TicketStatus, TicketPriority, AuditAction } from '@/types';
import Ticket from '@/models/Ticket';
import User from '@/models/User';
import AuditLog from '@/models/AuditLog';
import { validateTicketCreation, validateTicketUpdate, validatePagination } from '@/utils/validation';

/**
 * Get tickets (filtered by tenant and user role)
 */
export const getTickets = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId, userId, role } = req.user;
    const { 
      page = 1, 
      limit = 20, 
      status, 
      priority, 
      category, 
      assignedTo, 
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Validate pagination
    const paginationValidation = validatePagination({ page, limit, sort: sortBy, order: sortOrder });
    if (!paginationValidation.isValid) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid pagination parameters',
        errors: paginationValidation.errors
      };
      res.status(400).json(response);
      return;
    }

    // Build filter based on user role
    const filter: any = { customerId, deletedAt: null };
    
    // Regular users can only see their own tickets
    if (role === 'User') {
      filter.userId = userId;
    }
    
    // Apply additional filters
    if (status) filter.status = status;
    if (priority) filter.priority = priority;
    if (category) filter.category = category;
    if (assignedTo) filter.assignedTo = assignedTo;
    
    // Search functionality
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { category: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Build sort object
    const sort: any = {};
    sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

    // Get tickets with user details
    const [tickets, total] = await Promise.all([
      Ticket.find(filter)
        .populate('userId', 'email profile')
        .populate('assignedTo', 'email profile')
        .sort(sort)
        .skip(skip)
        .limit(limitNum)
        .lean(),
      Ticket.countDocuments(filter)
    ]);

    const response: ApiResponse = {
      success: true,
      data: tickets,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Get tickets error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get tickets'
    };
    res.status(500).json(response);
  }
};

/**
 * Create new ticket
 */
export const createTicket = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId, userId } = req.user;
    const ticketData = { ...req.body, customerId, userId };

    // Validate input
    const validation = validateTicketCreation(ticketData);
    if (!validation.isValid) {
      const response: ApiResponse = {
        success: false,
        error: 'Validation failed',
        errors: validation.errors
      };
      res.status(400).json(response);
      return;
    }

    // Create ticket
    const ticket = await Ticket.create(ticketData);

    // Populate user details
    await ticket.populate('userId', 'email profile');

    // Log ticket creation
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    
    await AuditLog.logAction(
      customerId,
      userId,
      AuditAction.TICKET_CREATE,
      'Ticket',
      { 
        ticketId: ticket._id.toString(),
        title: ticket.title,
        priority: ticket.priority,
        category: ticket.category
      },
      ticket._id.toString(),
      ipAddress,
      userAgent
    );

    // TODO: Trigger n8n workflow here
    // await triggerN8nWorkflow(ticket);

    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`tenant_${customerId}`).emit('ticket_created', {
        ticket: ticket.toJSON(),
        user: req.user
      });
    }

    const response: ApiResponse = {
      success: true,
      data: ticket,
      message: 'Ticket created successfully'
    };

    res.status(201).json(response);
  } catch (error) {
    console.error('Create ticket error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to create ticket'
    };
    res.status(500).json(response);
  }
};

/**
 * Get ticket by ID
 */
export const getTicketById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId, userId, role } = req.user;
    const { id } = req.params;

    // Build filter based on user role
    const filter: any = { _id: id, customerId, deletedAt: null };
    
    // Regular users can only see their own tickets
    if (role === 'User') {
      filter.userId = userId;
    }

    const ticket = await Ticket.findOne(filter)
      .populate('userId', 'email profile')
      .populate('assignedTo', 'email profile')
      .populate('comments.userId', 'email profile');

    if (!ticket) {
      const response: ApiResponse = {
        success: false,
        error: 'Ticket not found'
      };
      res.status(404).json(response);
      return;
    }

    const response: ApiResponse = {
      success: true,
      data: ticket
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Get ticket by ID error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get ticket'
    };
    res.status(500).json(response);
  }
};

/**
 * Update ticket
 */
export const updateTicket = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId, userId, role } = req.user;
    const { id } = req.params;
    const updates = req.body;

    // Validate input
    const validation = validateTicketUpdate(updates);
    if (!validation.isValid) {
      const response: ApiResponse = {
        success: false,
        error: 'Validation failed',
        errors: validation.errors
      };
      res.status(400).json(response);
      return;
    }

    // Build filter based on user role
    const filter: any = { _id: id, customerId, deletedAt: null };
    
    // Regular users can only update their own tickets
    if (role === 'User') {
      filter.userId = userId;
    }

    const ticket = await Ticket.findOne(filter);
    if (!ticket) {
      const response: ApiResponse = {
        success: false,
        error: 'Ticket not found'
      };
      res.status(404).json(response);
      return;
    }

    // Store original values for audit log
    const originalValues: any = {};
    const updatedFields: string[] = [];

    // Update fields
    Object.keys(updates).forEach(key => {
      if (ticket.schema.paths[key] && updates[key] !== undefined) {
        originalValues[key] = (ticket as any)[key];
        (ticket as any)[key] = updates[key];
        updatedFields.push(key);
      }
    });

    await ticket.save();

    // Populate user details
    await ticket.populate('userId', 'email profile');
    await ticket.populate('assignedTo', 'email profile');

    // Log ticket update
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    
    await AuditLog.logAction(
      customerId,
      userId,
      AuditAction.TICKET_UPDATE,
      'Ticket',
      { 
        ticketId: ticket._id.toString(),
        updatedFields,
        originalValues,
        newValues: updates
      },
      ticket._id.toString(),
      ipAddress,
      userAgent
    );

    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`ticket_${ticket._id}`).emit('ticket_updated', {
        ticket: ticket.toJSON(),
        updatedFields,
        user: req.user
      });
    }

    const response: ApiResponse = {
      success: true,
      data: ticket,
      message: 'Ticket updated successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Update ticket error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to update ticket'
    };
    res.status(500).json(response);
  }
};

/**
 * Delete ticket (soft delete)
 */
export const deleteTicket = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId, userId, role } = req.user;
    const { id } = req.params;

    // Build filter based on user role
    const filter: any = { _id: id, customerId, deletedAt: null };
    
    // Regular users can only delete their own tickets
    if (role === 'User') {
      filter.userId = userId;
    }

    const ticket = await Ticket.findOne(filter);
    if (!ticket) {
      const response: ApiResponse = {
        success: false,
        error: 'Ticket not found'
      };
      res.status(404).json(response);
      return;
    }

    // Soft delete
    ticket.softDelete();
    await ticket.save();

    // Log ticket deletion
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    
    await AuditLog.logAction(
      customerId,
      userId,
      AuditAction.TICKET_DELETE,
      'Ticket',
      { 
        ticketId: ticket._id.toString(),
        title: ticket.title
      },
      ticket._id.toString(),
      ipAddress,
      userAgent
    );

    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`tenant_${customerId}`).emit('ticket_deleted', {
        ticketId: ticket._id.toString(),
        user: req.user
      });
    }

    const response: ApiResponse = {
      success: true,
      message: 'Ticket deleted successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Delete ticket error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to delete ticket'
    };
    res.status(500).json(response);
  }
};

/**
 * Add comment to ticket
 */
export const addTicketComment = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId, userId, role } = req.user;
    const { id } = req.params;
    const { content } = req.body;

    if (!content || content.trim().length === 0) {
      const response: ApiResponse = {
        success: false,
        error: 'Comment content is required'
      };
      res.status(400).json(response);
      return;
    }

    // Build filter based on user role
    const filter: any = { _id: id, customerId, deletedAt: null };
    
    // Regular users can only comment on their own tickets
    if (role === 'User') {
      filter.userId = userId;
    }

    const ticket = await Ticket.findOne(filter);
    if (!ticket) {
      const response: ApiResponse = {
        success: false,
        error: 'Ticket not found'
      };
      res.status(404).json(response);
      return;
    }

    // Add comment
    ticket.addComment(userId, content.trim());
    await ticket.save();

    // Populate the new comment
    await ticket.populate('comments.userId', 'email profile');

    // Get the newly added comment
    const newComment = ticket.comments[ticket.comments.length - 1];

    // Emit real-time event
    const io = req.app.get('io');
    if (io) {
      io.to(`ticket_${ticket._id}`).emit('comment_added', {
        ticketId: ticket._id.toString(),
        comment: newComment,
        user: req.user
      });
    }

    const response: ApiResponse = {
      success: true,
      data: newComment,
      message: 'Comment added successfully'
    };

    res.status(201).json(response);
  } catch (error) {
    console.error('Add ticket comment error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to add comment'
    };
    res.status(500).json(response);
  }
};
