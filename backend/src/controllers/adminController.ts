import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, UserRole, AuditAction } from '@/types';
import User from '@/models/User';
import Ticket from '@/models/Ticket';
import AuditLog from '@/models/AuditLog';
import { 
  getTenantConfig, 
  updateTenantConfig, 
  addTenantScreen, 
  updateTenantScreen, 
  removeTenantScreen,
  getRegistryStats,
  validateTenantConfig
} from '@/services/registryService';
import { getTenantStats } from '@/middleware/tenant';
import { validateUserRegistration, validatePagination } from '@/utils/validation';

/**
 * Get all users in tenant
 */
export const getTenantUsers = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId } = req.user;
    const { page = 1, limit = 20, role, isActive, search } = req.query;

    // Validate pagination
    const paginationValidation = validatePagination({ page, limit });
    if (!paginationValidation.isValid) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid pagination parameters',
        errors: paginationValidation.errors
      };
      res.status(400).json(response);
      return;
    }

    // Build filter
    const filter: any = { customerId, deletedAt: null };
    
    if (role) filter.role = role;
    if (isActive !== undefined) filter.isActive = isActive === 'true';
    if (search) {
      filter.$or = [
        { email: { $regex: search, $options: 'i' } },
        { 'profile.firstName': { $regex: search, $options: 'i' } },
        { 'profile.lastName': { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Get users
    const [users, total] = await Promise.all([
      User.find(filter)
        .select('-password -refreshTokens')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limitNum)
        .lean(),
      User.countDocuments(filter)
    ]);

    const response: ApiResponse = {
      success: true,
      data: users,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Get tenant users error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get tenant users'
    };
    res.status(500).json(response);
  }
};

/**
 * Create new user in tenant
 */
export const createTenantUser = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId } = req.user;
    const userData = { ...req.body, customerId };

    // Validate input
    const validation = validateUserRegistration(userData);
    if (!validation.isValid) {
      const response: ApiResponse = {
        success: false,
        error: 'Validation failed',
        errors: validation.errors
      };
      res.status(400).json(response);
      return;
    }

    // Check if user already exists
    const existingUser = await User.findByEmail(userData.email, customerId);
    if (existingUser) {
      const response: ApiResponse = {
        success: false,
        error: 'User with this email already exists in tenant'
      };
      res.status(409).json(response);
      return;
    }

    // Create user
    const user = await User.create(userData);

    // Log user creation
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    
    await AuditLog.logAction(
      customerId,
      req.user.userId,
      AuditAction.USER_CREATE,
      'User',
      { 
        createdUserId: user._id.toString(),
        email: user.email,
        role: user.role
      },
      user._id.toString(),
      ipAddress,
      userAgent
    );

    const response: ApiResponse = {
      success: true,
      data: {
        id: user._id,
        email: user.email,
        role: user.role,
        profile: user.profile,
        isActive: user.isActive,
        createdAt: user.createdAt
      },
      message: 'User created successfully'
    };

    res.status(201).json(response);
  } catch (error) {
    console.error('Create tenant user error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to create user'
    };
    res.status(500).json(response);
  }
};

/**
 * Update user in tenant
 */
export const updateTenantUser = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId } = req.user;
    const { userId } = req.params;
    const { role, isActive, profile } = req.body;

    const user = await User.findOne({ _id: userId, customerId });
    if (!user) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      res.status(404).json(response);
      return;
    }

    // Update fields
    if (role && Object.values(UserRole).includes(role)) {
      user.role = role;
    }
    if (isActive !== undefined) {
      user.isActive = isActive;
    }
    if (profile) {
      if (profile.firstName) user.profile.firstName = profile.firstName;
      if (profile.lastName) user.profile.lastName = profile.lastName;
      if (profile.avatar) user.profile.avatar = profile.avatar;
    }

    await user.save();

    // Log user update
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    
    await AuditLog.logAction(
      customerId,
      req.user.userId,
      AuditAction.USER_UPDATE,
      'User',
      { 
        updatedUserId: user._id.toString(),
        updatedFields: Object.keys(req.body),
        email: user.email
      },
      user._id.toString(),
      ipAddress,
      userAgent
    );

    const response: ApiResponse = {
      success: true,
      data: {
        id: user._id,
        email: user.email,
        role: user.role,
        profile: user.profile,
        isActive: user.isActive,
        updatedAt: user.updatedAt
      },
      message: 'User updated successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Update tenant user error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to update user'
    };
    res.status(500).json(response);
  }
};

/**
 * Get tenant audit logs
 */
export const getTenantAuditLogs = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId } = req.user;
    const { 
      page = 1, 
      limit = 50, 
      userId, 
      action, 
      resourceType, 
      startDate, 
      endDate 
    } = req.query;

    // Build filter
    const filter: any = { customerId, deletedAt: null };
    
    if (userId) filter.userId = userId;
    if (action) filter.action = action;
    if (resourceType) filter.resourceType = resourceType;
    
    if (startDate || endDate) {
      filter.timestamp = {};
      if (startDate) filter.timestamp.$gte = new Date(startDate as string);
      if (endDate) filter.timestamp.$lte = new Date(endDate as string);
    }

    // Calculate pagination
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Get audit logs with user details
    const [auditLogs, total] = await Promise.all([
      AuditLog.find(filter)
        .populate('userId', 'email profile')
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limitNum)
        .lean(),
      AuditLog.countDocuments(filter)
    ]);

    const response: ApiResponse = {
      success: true,
      data: auditLogs,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Get tenant audit logs error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get audit logs'
    };
    res.status(500).json(response);
  }
};

/**
 * Get tenant analytics
 */
export const getTenantAnalytics = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId } = req.user;

    // Get basic tenant statistics
    const tenantStats = await getTenantStats(customerId);

    // Get ticket statistics
    const ticketStats = await Ticket.getTicketStats(customerId);
    const ticketsByStatus = ticketStats.reduce((acc: any, stat: any) => {
      acc[stat._id] = stat.count;
      return acc;
    }, {});

    // Get user role distribution
    const userRoleStats = await User.aggregate([
      { $match: { customerId, deletedAt: null } },
      { $group: { _id: '$role', count: { $sum: 1 } } }
    ]);

    const usersByRole = userRoleStats.reduce((acc: any, stat: any) => {
      acc[stat._id] = stat.count;
      return acc;
    }, {});

    // Get recent activity
    const recentActivity = await AuditLog.getRecentActivity(customerId, 10);

    // Get user activity stats
    const userActivityStats = await AuditLog.getUserActivityStats(customerId);

    const response: ApiResponse = {
      success: true,
      data: {
        overview: tenantStats,
        tickets: {
          byStatus: ticketsByStatus,
          total: Object.values(ticketsByStatus).reduce((sum: number, count: any) => sum + count, 0)
        },
        users: {
          byRole: usersByRole,
          total: tenantStats.users
        },
        recentActivity,
        userActivity: userActivityStats.slice(0, 10) // Top 10 most active users
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Get tenant analytics error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get tenant analytics'
    };
    res.status(500).json(response);
  }
};

/**
 * Get tenant configuration
 */
export const getTenantConfiguration = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId } = req.user;

    const tenantConfig = await getTenantConfig(customerId);
    if (!tenantConfig) {
      const response: ApiResponse = {
        success: false,
        error: 'Tenant configuration not found'
      };
      res.status(404).json(response);
      return;
    }

    const response: ApiResponse = {
      success: true,
      data: tenantConfig
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Get tenant configuration error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get tenant configuration'
    };
    res.status(500).json(response);
  }
};

/**
 * Update tenant configuration
 */
export const updateTenantConfiguration = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId } = req.user;
    const updates = req.body;

    // Validate configuration
    const validationErrors = validateTenantConfig(updates);
    if (validationErrors.length > 0) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid tenant configuration',
        errors: validationErrors.map(error => ({ field: 'config', message: error }))
      };
      res.status(400).json(response);
      return;
    }

    const updatedConfig = await updateTenantConfig(customerId, updates);

    const response: ApiResponse = {
      success: true,
      data: updatedConfig,
      message: 'Tenant configuration updated successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Update tenant configuration error:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update tenant configuration'
    };
    res.status(500).json(response);
  }
};
