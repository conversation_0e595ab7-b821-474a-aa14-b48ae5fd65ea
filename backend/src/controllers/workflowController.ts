import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AuditAction } from '@/types';
import WorkflowExecution, { WorkflowExecutionStatus } from '@/models/WorkflowExecution';
import AuditLog from '@/models/AuditLog';
import { n8nService, WORKFLOW_IDS } from '@/services/n8nService';
import { validatePagination } from '@/utils/validation';

/**
 * Get workflow executions for tenant
 */
export const getWorkflowExecutions = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId } = req.user;
    const { 
      page = 1, 
      limit = 20, 
      workflowId, 
      status, 
      triggerType,
      startDate,
      endDate
    } = req.query;

    // Validate pagination
    const paginationValidation = validatePagination({ page, limit });
    if (!paginationValidation.isValid) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid pagination parameters',
        errors: paginationValidation.errors
      };
      res.status(400).json(response);
      return;
    }

    // Build filter
    const filter: any = { customerId };
    
    if (workflowId) filter.workflowId = workflowId;
    if (status) filter.status = status;
    if (triggerType) filter.triggerType = triggerType;
    
    if (startDate || endDate) {
      filter.startedAt = {};
      if (startDate) filter.startedAt.$gte = new Date(startDate as string);
      if (endDate) filter.startedAt.$lte = new Date(endDate as string);
    }

    // Calculate pagination
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Get executions
    const [executions, total] = await Promise.all([
      WorkflowExecution.find(filter)
        .sort({ startedAt: -1 })
        .skip(skip)
        .limit(limitNum)
        .lean(),
      WorkflowExecution.countDocuments(filter)
    ]);

    const response: ApiResponse = {
      success: true,
      data: executions,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Get workflow executions error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get workflow executions'
    };
    res.status(500).json(response);
  }
};

/**
 * Get workflow execution by ID
 */
export const getWorkflowExecution = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId } = req.user;
    const { executionId } = req.params;

    const execution = await WorkflowExecution.findOne({ 
      executionId, 
      customerId 
    });

    if (!execution) {
      const response: ApiResponse = {
        success: false,
        error: 'Workflow execution not found'
      };
      res.status(404).json(response);
      return;
    }

    const response: ApiResponse = {
      success: true,
      data: execution
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Get workflow execution error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get workflow execution'
    };
    res.status(500).json(response);
  }
};

/**
 * Trigger workflow manually
 */
export const triggerWorkflow = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId, userId } = req.user;
    const { workflowId, data = {} } = req.body;

    if (!workflowId) {
      const response: ApiResponse = {
        success: false,
        error: 'Workflow ID is required'
      };
      res.status(400).json(response);
      return;
    }

    // Trigger workflow via n8n
    const result = await n8nService.triggerCustomWebhook(workflowId, {
      ...data,
      customerId,
      triggeredBy: userId,
      triggerType: 'manual'
    });

    // Create execution record
    const execution = await WorkflowExecution.createExecution(
      customerId,
      workflowId,
      workflowId, // Use workflowId as name for now
      result.executionId || `manual-${Date.now()}`,
      'manual',
      data,
      {
        triggeredBy: userId,
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip
      }
    );

    // Log workflow trigger
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    
    await AuditLog.logAction(
      customerId,
      userId,
      AuditAction.WORKFLOW_TRIGGER,
      'Workflow',
      { 
        workflowId,
        executionId: execution.executionId,
        triggerType: 'manual'
      },
      execution._id.toString(),
      ipAddress,
      userAgent
    );

    const response: ApiResponse = {
      success: true,
      data: {
        execution,
        result
      },
      message: 'Workflow triggered successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Trigger workflow error:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to trigger workflow'
    };
    res.status(500).json(response);
  }
};

/**
 * Retry failed workflow execution
 */
export const retryWorkflowExecution = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId, userId } = req.user;
    const { executionId } = req.params;

    const execution = await WorkflowExecution.findOne({ 
      executionId, 
      customerId 
    });

    if (!execution) {
      const response: ApiResponse = {
        success: false,
        error: 'Workflow execution not found'
      };
      res.status(404).json(response);
      return;
    }

    if (!execution.canRetry()) {
      const response: ApiResponse = {
        success: false,
        error: 'Workflow execution cannot be retried'
      };
      res.status(400).json(response);
      return;
    }

    // Increment retry count and reset status
    await WorkflowExecution.incrementRetryCount(executionId);

    // Trigger workflow again
    const result = await n8nService.triggerCustomWebhook(execution.workflowId, {
      ...execution.triggerData,
      customerId,
      retryCount: execution.retryCount + 1,
      originalExecutionId: executionId
    });

    // Log workflow retry
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    
    await AuditLog.logAction(
      customerId,
      userId,
      AuditAction.WORKFLOW_RETRY,
      'Workflow',
      { 
        workflowId: execution.workflowId,
        executionId,
        retryCount: execution.retryCount + 1
      },
      execution._id.toString(),
      ipAddress,
      userAgent
    );

    const response: ApiResponse = {
      success: true,
      data: {
        execution: await WorkflowExecution.findOne({ executionId, customerId }),
        result
      },
      message: 'Workflow execution retried successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Retry workflow execution error:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to retry workflow execution'
    };
    res.status(500).json(response);
  }
};

/**
 * Cancel running workflow execution
 */
export const cancelWorkflowExecution = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId, userId } = req.user;
    const { executionId } = req.params;

    const execution = await WorkflowExecution.findOne({ 
      executionId, 
      customerId 
    });

    if (!execution) {
      const response: ApiResponse = {
        success: false,
        error: 'Workflow execution not found'
      };
      res.status(404).json(response);
      return;
    }

    if (execution.status !== WorkflowExecutionStatus.RUNNING && 
        execution.status !== WorkflowExecutionStatus.WAITING) {
      const response: ApiResponse = {
        success: false,
        error: 'Workflow execution cannot be cancelled'
      };
      res.status(400).json(response);
      return;
    }

    // Mark as cancelled
    await execution.markAsCancelled();

    // Log workflow cancellation
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    
    await AuditLog.logAction(
      customerId,
      userId,
      AuditAction.WORKFLOW_CANCEL,
      'Workflow',
      { 
        workflowId: execution.workflowId,
        executionId
      },
      execution._id.toString(),
      ipAddress,
      userAgent
    );

    const response: ApiResponse = {
      success: true,
      data: execution,
      message: 'Workflow execution cancelled successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Cancel workflow execution error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to cancel workflow execution'
    };
    res.status(500).json(response);
  }
};

/**
 * Get workflow statistics
 */
export const getWorkflowStats = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { customerId } = req.user;

    const [executionStats, workflowStats, recentExecutions, failedExecutions] = await Promise.all([
      WorkflowExecution.getExecutionStats(customerId),
      WorkflowExecution.getWorkflowStats(customerId),
      WorkflowExecution.getRecentExecutions(customerId, 10),
      WorkflowExecution.getFailedExecutions(customerId, 5)
    ]);

    const response: ApiResponse = {
      success: true,
      data: {
        executionStats,
        workflowStats,
        recentExecutions,
        failedExecutions
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Get workflow stats error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get workflow statistics'
    };
    res.status(500).json(response);
  }
};

/**
 * Get available workflows
 */
export const getAvailableWorkflows = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    // Get workflows from n8n
    const workflows = await n8nService.listWorkflows();

    // Filter and format workflows
    const availableWorkflows = Object.entries(WORKFLOW_IDS).map(([key, workflowId]) => ({
      id: workflowId,
      name: key.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
      description: `Automated workflow for ${key.toLowerCase().replace(/_/g, ' ')}`,
      type: key.toLowerCase()
    }));

    const response: ApiResponse = {
      success: true,
      data: {
        configured: availableWorkflows,
        available: workflows?.data || []
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Get available workflows error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get available workflows'
    };
    res.status(500).json(response);
  }
};

/**
 * Test n8n connection
 */
export const testN8nConnection = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const isHealthy = await n8nService.healthCheck();

    const response: ApiResponse = {
      success: true,
      data: {
        connected: isHealthy,
        timestamp: new Date().toISOString()
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Test n8n connection error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to test n8n connection'
    };
    res.status(500).json(response);
  }
};
