import { Request, Response } from 'express';
import { AuthenticatedRequest, LoginRequest, LoginResponse, ApiResponse, AuditAction } from '@/types';
import User from '@/models/User';
import AuditLog from '@/models/AuditLog';
import { generateTokenPair, verifyRefreshToken, generatePasswordResetToken, verifyPasswordResetToken } from '@/utils/jwt';
import { validateLoginInput, validatePasswordResetInput } from '@/utils/validation';

/**
 * User login
 */
export const login = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, password, customerId }: LoginRequest = req.body;

    // Validate input
    const validation = validateLoginInput({ email, password, customerId });
    if (!validation.isValid) {
      const response: ApiResponse = {
        success: false,
        error: 'Validation failed',
        errors: validation.errors
      };
      res.status(400).json(response);
      return;
    }

    // Find user by email and tenant
    const user = await User.findByEmail(email, customerId || 'default');
    if (!user) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid credentials'
      };
      res.status(401).json(response);
      return;
    }

    // Check if user is active
    if (!user.isActive) {
      const response: ApiResponse = {
        success: false,
        error: 'Account is deactivated'
      };
      res.status(401).json(response);
      return;
    }

    // Verify password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid credentials'
      };
      res.status(401).json(response);
      return;
    }

    // Generate token pair
    const tokens = generateTokenPair({
      userId: user._id.toString(),
      customerId: user.customerId,
      email: user.email,
      role: user.role
    });

    // Add refresh token to user
    user.addRefreshToken(tokens.refreshToken);
    user.lastLogin = new Date();
    await user.save();

    // Log successful login
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    
    await AuditLog.logAction(
      user.customerId,
      user._id.toString(),
      AuditAction.LOGIN,
      'User',
      { email: user.email },
      user._id.toString(),
      ipAddress,
      userAgent
    );

    // Prepare response
    const response: ApiResponse<LoginResponse> = {
      success: true,
      data: {
        user: {
          id: user._id.toString(),
          email: user.email,
          role: user.role,
          profile: user.profile,
          customerId: user.customerId
        },
        tokens
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Login error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Login failed'
    };
    res.status(500).json(response);
  }
};

/**
 * User logout
 */
export const logout = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      const response: ApiResponse = {
        success: false,
        error: 'Refresh token required'
      };
      res.status(400).json(response);
      return;
    }

    // Find user and remove refresh token
    const user = await User.findById(req.user.userId);
    if (user) {
      user.removeRefreshToken(refreshToken);
      await user.save();

      // Log logout
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent') || 'unknown';
      
      await AuditLog.logAction(
        user.customerId,
        user._id.toString(),
        AuditAction.LOGOUT,
        'User',
        {},
        user._id.toString(),
        ipAddress,
        userAgent
      );
    }

    const response: ApiResponse = {
      success: true,
      message: 'Logged out successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Logout error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Logout failed'
    };
    res.status(500).json(response);
  }
};

/**
 * Refresh access token
 */
export const refreshToken = async (req: Request, res: Response): Promise<void> => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      const response: ApiResponse = {
        success: false,
        error: 'Refresh token required'
      };
      res.status(400).json(response);
      return;
    }

    // Verify refresh token
    let decoded;
    try {
      decoded = verifyRefreshToken(refreshToken);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Invalid refresh token'
      };
      res.status(401).json(response);
      return;
    }

    // Find user and verify refresh token exists
    const user = await User.findById(decoded.userId);
    if (!user || !user.isActive || !user.refreshTokens.includes(refreshToken)) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid refresh token'
      };
      res.status(401).json(response);
      return;
    }

    // Generate new token pair
    const tokens = generateTokenPair({
      userId: user._id.toString(),
      customerId: user.customerId,
      email: user.email,
      role: user.role
    });

    // Replace old refresh token with new one
    user.removeRefreshToken(refreshToken);
    user.addRefreshToken(tokens.refreshToken);
    await user.save();

    const response: ApiResponse = {
      success: true,
      data: { tokens }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Token refresh error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Token refresh failed'
    };
    res.status(500).json(response);
  }
};

/**
 * Request password reset
 */
export const forgotPassword = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, customerId } = req.body;

    if (!email || !customerId) {
      const response: ApiResponse = {
        success: false,
        error: 'Email and customer ID are required'
      };
      res.status(400).json(response);
      return;
    }

    // Find user
    const user = await User.findByEmail(email, customerId);
    if (!user) {
      // Don't reveal if user exists or not
      const response: ApiResponse = {
        success: true,
        message: 'If the email exists, a password reset link has been sent'
      };
      res.status(200).json(response);
      return;
    }

    // Generate password reset token
    const resetToken = generatePasswordResetToken(user._id.toString(), user.email);
    
    // Save reset token to user (expires in 1 hour)
    user.passwordResetToken = resetToken;
    user.passwordResetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
    await user.save();

    // In a real application, send email here
    // For demo purposes, we'll just log it
    console.log(`Password reset token for ${email}: ${resetToken}`);

    // Log password reset request
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    
    await AuditLog.logAction(
      user.customerId,
      user._id.toString(),
      AuditAction.PASSWORD_RESET,
      'User',
      { email: user.email, action: 'request' },
      user._id.toString(),
      ipAddress,
      userAgent
    );

    const response: ApiResponse = {
      success: true,
      message: 'If the email exists, a password reset link has been sent'
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Forgot password error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Password reset request failed'
    };
    res.status(500).json(response);
  }
};

/**
 * Reset password
 */
export const resetPassword = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token, newPassword } = req.body;

    // Validate input
    const validation = validatePasswordResetInput({ token, newPassword });
    if (!validation.isValid) {
      const response: ApiResponse = {
        success: false,
        error: 'Validation failed',
        errors: validation.errors
      };
      res.status(400).json(response);
      return;
    }

    // Verify reset token
    let decoded;
    try {
      decoded = verifyPasswordResetToken(token);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Invalid reset token'
      };
      res.status(400).json(response);
      return;
    }

    // Find user and verify reset token
    const user = await User.findById(decoded.userId);
    if (!user || user.passwordResetToken !== token || !user.passwordResetExpires || user.passwordResetExpires < new Date()) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid or expired reset token'
      };
      res.status(400).json(response);
      return;
    }

    // Update password and clear reset token
    user.password = newPassword;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    user.clearRefreshTokens(); // Invalidate all existing sessions
    await user.save();

    // Log password reset completion
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    
    await AuditLog.logAction(
      user.customerId,
      user._id.toString(),
      AuditAction.PASSWORD_RESET,
      'User',
      { email: user.email, action: 'complete' },
      user._id.toString(),
      ipAddress,
      userAgent
    );

    const response: ApiResponse = {
      success: true,
      message: 'Password reset successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Reset password error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Password reset failed'
    };
    res.status(500).json(response);
  }
};
