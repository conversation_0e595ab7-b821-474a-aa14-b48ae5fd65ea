import { Request, Response } from 'express';
import { ApiResponse } from '@/types';
import WorkflowExecution, { WorkflowExecutionStatus } from '@/models/WorkflowExecution';
import Ticket from '@/models/Ticket';
import User from '@/models/User';

/**
 * Handle n8n workflow completion callback
 */
export const handleWorkflowCompletion = async (req: Request, res: Response): Promise<void> => {
  try {
    const { 
      executionId, 
      workflowId, 
      status, 
      result, 
      error,
      customerId 
    } = req.body;

    if (!executionId || !workflowId || !status) {
      const response: ApiResponse = {
        success: false,
        error: 'Missing required fields: executionId, workflowId, status'
      };
      res.status(400).json(response);
      return;
    }

    // Update workflow execution status
    const execution = await WorkflowExecution.updateExecutionStatus(
      executionId,
      status as WorkflowExecutionStatus,
      result,
      error
    );

    if (!execution) {
      const response: ApiResponse = {
        success: false,
        error: 'Workflow execution not found'
      };
      res.status(404).json(response);
      return;
    }

    console.log(`Workflow ${workflowId} execution ${executionId} completed with status: ${status}`);

    const response: ApiResponse = {
      success: true,
      data: execution,
      message: 'Workflow status updated successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Handle workflow completion error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to handle workflow completion'
    };
    res.status(500).json(response);
  }
};

/**
 * Handle ticket workflow completion (e.g., ticket resolved, assigned, etc.)
 */
export const handleTicketWorkflowCompletion = async (req: Request, res: Response): Promise<void> => {
  try {
    const { 
      executionId,
      ticketId,
      action,
      result,
      customerId,
      error
    } = req.body;

    if (!executionId || !ticketId || !action) {
      const response: ApiResponse = {
        success: false,
        error: 'Missing required fields: executionId, ticketId, action'
      };
      res.status(400).json(response);
      return;
    }

    // Find the ticket
    const ticket = await Ticket.findOne({ _id: ticketId, customerId });
    if (!ticket) {
      const response: ApiResponse = {
        success: false,
        error: 'Ticket not found'
      };
      res.status(404).json(response);
      return;
    }

    // Update workflow execution
    const status = error ? WorkflowExecutionStatus.ERROR : WorkflowExecutionStatus.SUCCESS;
    await WorkflowExecution.updateExecutionStatus(executionId, status, result, error);

    // Handle specific actions based on workflow result
    if (!error && result) {
      switch (action) {
        case 'auto-assign':
          if (result.assignedTo) {
            const assignedUser = await User.findOne({ 
              _id: result.assignedTo, 
              customerId 
            });
            if (assignedUser) {
              ticket.assignedTo = assignedUser._id;
              ticket.addComment(
                'system',
                `Ticket automatically assigned to ${assignedUser.profile.firstName} ${assignedUser.profile.lastName} via workflow`
              );
              await ticket.save();
            }
          }
          break;

        case 'auto-resolve':
          if (result.resolution) {
            ticket.status = 'Resolved';
            ticket.addComment('system', `Ticket automatically resolved: ${result.resolution}`);
            await ticket.save();
          }
          break;

        case 'escalate':
          if (result.escalatedTo) {
            ticket.priority = 'High';
            ticket.addComment('system', `Ticket escalated due to: ${result.reason || 'workflow automation'}`);
            await ticket.save();
          }
          break;

        case 'notify':
          // Notification handling would go here
          console.log(`Notification sent for ticket ${ticketId}: ${result.message}`);
          break;

        default:
          console.log(`Unknown ticket action: ${action}`);
      }
    }

    console.log(`Ticket workflow ${action} completed for ticket ${ticketId}`);

    const response: ApiResponse = {
      success: true,
      data: {
        ticket: await Ticket.findById(ticketId).populate('userId assignedTo'),
        action,
        result
      },
      message: 'Ticket workflow completed successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Handle ticket workflow completion error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to handle ticket workflow completion'
    };
    res.status(500).json(response);
  }
};

/**
 * Handle n8n health status updates
 */
export const handleN8nHealthStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const { status, timestamp, workflows } = req.body;

    console.log(`n8n health status update: ${status} at ${timestamp}`);
    
    if (workflows) {
      console.log(`Active workflows: ${workflows.length}`);
    }

    // Here you could store health status in database or trigger alerts
    // For now, just log and acknowledge

    const response: ApiResponse = {
      success: true,
      message: 'Health status received'
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Handle n8n health status error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to handle health status'
    };
    res.status(500).json(response);
  }
};

/**
 * Handle generic n8n webhook callbacks
 */
export const handleGenericWebhook = async (req: Request, res: Response): Promise<void> => {
  try {
    const { type, data, executionId } = req.body;

    console.log(`Generic webhook received: ${type}`, { executionId, data });

    // Update execution if executionId is provided
    if (executionId) {
      await WorkflowExecution.updateExecutionStatus(
        executionId,
        WorkflowExecutionStatus.SUCCESS,
        data
      );
    }

    const response: ApiResponse = {
      success: true,
      message: 'Webhook processed successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Handle generic webhook error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to handle webhook'
    };
    res.status(500).json(response);
  }
};
