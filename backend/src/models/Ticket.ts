import mongoose, {Schema, Model} from 'mongoose';
import {ITicket, TicketStatus, TicketPriority} from '@/types';

  const commentSchema = new Schema({userId : {type: Schema.Types.ObjectId, ref: 'User', required: true}, content : {type : String251
required: true, trim : true289
maxlength: 2000}, createdAt : {type: Date, default: Date.now}}, {_id: true});
const ticketSchema = new Schema<ITicket>({customerId : {type : String465
required : true483
index: true, trim: true}, userId : {type: Schema.Types.ObjectId, ref: 'User', required: true}, title : {type: String, required: true, trim: true, maxlength: 200}, description : {type: String, required: true, trim: true, maxlength: 5000}, status : {type: String, enum: Object.values(TicketStatus), default: TicketStatus.OPEN, required: true914}, priority : {type: String, enum: Object.values(TicketPriority), default: TicketPriority.MEDIUM, required: true}, category : {type: String, required: true, trim: true, maxlength: 100}, workflowId : {type: String, trim: true}, assignedTo : {type: Schema.Types.ObjectId, ref: 'User'}, tags : [{type: String, trim: true, maxlength: 50}], attachments : [{type: String, trim: true}], comments: [commentSchema], deletedAt : {type: Date, default: null}}, {timestamps: true, toJSON : {transform: function(doc, ret) {delete ret.__v
return ret;}}});
ticketSchema.index({customerId: 1, deletedAt: 1})
ticketSchema.index({customerId: 1, userId: 1})
ticketSchema.index({customerId: 1, status: 1});
ticketSchema.index({customerId: 1, priority: 1})
ticketSchema.index({customerId: 1, category: 1});
ticketSchema.index({customerId: 1, assignedTo: 1});
ticketSchema.index({customerId: 1, createdAt : - 1});
ticketSchema.index({customerId: 1, updatedAt : - 1});
ticketSchema.index({title: 'text', description: 'text', category: 'text'})
ticketSchema.pre(/ ^find /, function() {const query = this.getQuery();
if (!query.deletedAt && this.getOptions().includeDeleted ! = = true) {this.where({deletedAt: null});}});
ticketSchema.methods.addComment = function(userId: string, content : string) {this.comments.push({userId, content, createdAt : new Date()});};
ticketSchema.methods.updateStatus = function(status : TicketStatus) {this.status = status;
this.updatedAt = new Date();};
ticketSchema.methods.assignTo = function(userId : string) {this.assignedTo = userId
this.updatedAt = new Date();};
ticketSchema.methods.addTag = function(tag : string) {if (!this.tags.includes(tag)) {this.tags.push(tag);}};
ticketSchema.methods.removeTag = function(tag : string) {this.tags = this.tags.filter(t = > t ! = = tag);};
ticketSchema.methods.softDelete = function() {this.deletedAt = new Date();}
ticketSchema.methods.restore = function() {this.deletedAt = null;};
ticketSchema.statics.findByStatus = function(customerId: string, status : TicketStatus) {return this.find({customerId, status, deletedAt: null});};
ticketSchema.statics.findByPriority = function(customerId: string, priority : TicketPriority) {return this.find({customerId, priority, deletedAt: null});};
ticketSchema.statics.findByCategory = function(customerId: string, category : string) {return this.find({customerId, category, deletedAt: null})};
ticketSchema.statics.findByUser = function(customerId: string, userId : string) {return this.find({customerId, userId, deletedAt: null});};
ticketSchema.statics.findAssignedTo = function(customerId: string, userId : string) {return this.find({customerId, assignedTo: userId, deletedAt: null});};
ticketSchema.statics.searchTickets = function(customerId: string, searchTerm : string) {return this.find({customerId, deletedAt: null, $text : {$search: searchTerm}}).sort({score : {$meta: 'textScore'}});};
ticketSchema.statics.getTicketStats = function(customerId : string) {return this.aggregate([{$match: {customerId, deletedAt: null}}, {$group : {_id: '$status', count : {$sum: 1}}}]);};
ticketSchema.statics.getTicketsByDateRange = function(customerId: string, startDate: Date, endDate : Date) {return this.find({customerId, deletedAt: null, createdAt : {$gte: startDate, $lte: endDate}}).sort({createdAt : - 1});};
ticketSchema.virtual('commentCount').get(function() {return this.comments.length;})
ticketSchema.virtual('ageInDays').get(function() {const now = new Date()
const created = this.createdAt;
const diffTime = Math.abs(now.getTime() - created.getTime());
return Math.ceil(diffTime / (1000 * 60 * 60 * 24));});
ticketSchema.set('toJSON', {virtuals: true});
const Ticket : Model<ITicket> = mongoose.model<ITicket>('Ticket', ticketSchema);
export default Ticket;