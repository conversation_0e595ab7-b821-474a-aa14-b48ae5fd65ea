import mongoose, { Schema, Model } from 'mongoose';
import { IAuditLog, AuditAction } from '@/types';

// Audit log schema with tenant isolation
const auditLogSchema = new Schema<IAuditLog>({
  customerId: {
    type: String,
    required: true,
    index: true,
    trim: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  action: {
    type: String,
    enum: Object.values(AuditAction),
    required: true
  },
  resourceType: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  resourceId: {
    type: String,
    trim: true
  },
  details: {
    type: Schema.Types.Mixed,
    default: {}
  },
  ipAddress: {
    type: String,
    required: true,
    trim: true
  },
  userAgent: {
    type: String,
    required: true,
    trim: true
  },
  timestamp: {
    type: Date,
    default: Date.now,
    required: true
  },
  deletedAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes for performance and tenant isolation
auditLogSchema.index({ customerId: 1, deletedAt: 1 });
auditLogSchema.index({ customerId: 1, userId: 1 });
auditLogSchema.index({ customerId: 1, action: 1 });
auditLogSchema.index({ customerId: 1, resourceType: 1 });
auditLogSchema.index({ customerId: 1, resourceId: 1 });
auditLogSchema.index({ customerId: 1, timestamp: -1 });
auditLogSchema.index({ customerId: 1, timestamp: 1 }); // For date range queries

// TTL index for automatic cleanup (90 days)
auditLogSchema.index({ timestamp: 1 }, { 
  expireAfterSeconds: 90 * 24 * 60 * 60 // 90 days in seconds
});

// Pre-find middleware for tenant isolation and soft delete
auditLogSchema.pre(/^find/, function() {
  // Filter out soft-deleted records unless explicitly requested
  const query = this.getQuery();
  if (!query.deletedAt && this.getOptions().includeDeleted !== true) {
    this.where({ deletedAt: null });
  }
});

// Static methods
auditLogSchema.statics.logAction = async function(
  customerId: string,
  userId: string,
  action: AuditAction,
  resourceType: string,
  details: Record<string, any> = {},
  resourceId?: string,
  ipAddress: string = 'unknown',
  userAgent: string = 'unknown'
) {
  return this.create({
    customerId,
    userId,
    action,
    resourceType,
    resourceId,
    details,
    ipAddress,
    userAgent,
    timestamp: new Date()
  });
};

auditLogSchema.statics.findByUser = function(customerId: string, userId: string) {
  return this.find({ customerId, userId, deletedAt: null })
    .sort({ timestamp: -1 });
};

auditLogSchema.statics.findByAction = function(customerId: string, action: AuditAction) {
  return this.find({ customerId, action, deletedAt: null })
    .sort({ timestamp: -1 });
};

auditLogSchema.statics.findByResourceType = function(customerId: string, resourceType: string) {
  return this.find({ customerId, resourceType, deletedAt: null })
    .sort({ timestamp: -1 });
};

auditLogSchema.statics.findByResourceId = function(customerId: string, resourceId: string) {
  return this.find({ customerId, resourceId, deletedAt: null })
    .sort({ timestamp: -1 });
};

auditLogSchema.statics.findByDateRange = function(
  customerId: string,
  startDate: Date,
  endDate: Date
) {
  return this.find({
    customerId,
    deletedAt: null,
    timestamp: {
      $gte: startDate,
      $lte: endDate
    }
  }).sort({ timestamp: -1 });
};

auditLogSchema.statics.getActionStats = function(customerId: string) {
  return this.aggregate([
    { $match: { customerId, deletedAt: null } },
    {
      $group: {
        _id: '$action',
        count: { $sum: 1 }
      }
    },
    { $sort: { count: -1 } }
  ]);
};

auditLogSchema.statics.getUserActivityStats = function(customerId: string) {
  return this.aggregate([
    { $match: { customerId, deletedAt: null } },
    {
      $group: {
        _id: '$userId',
        actionCount: { $sum: 1 },
        lastActivity: { $max: '$timestamp' },
        actions: { $addToSet: '$action' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $unwind: '$user'
    },
    {
      $project: {
        userId: '$_id',
        actionCount: 1,
        lastActivity: 1,
        actions: 1,
        userEmail: '$user.email',
        userProfile: '$user.profile'
      }
    },
    { $sort: { actionCount: -1 } }
  ]);
};

auditLogSchema.statics.getRecentActivity = function(customerId: string, limit: number = 50) {
  return this.find({ customerId, deletedAt: null })
    .populate('userId', 'email profile')
    .sort({ timestamp: -1 })
    .limit(limit);
};

auditLogSchema.statics.searchLogs = function(
  customerId: string,
  filters: {
    userId?: string;
    action?: AuditAction;
    resourceType?: string;
    resourceId?: string;
    startDate?: Date;
    endDate?: Date;
    ipAddress?: string;
  }
) {
  const query: any = { customerId, deletedAt: null };

  if (filters.userId) query.userId = filters.userId;
  if (filters.action) query.action = filters.action;
  if (filters.resourceType) query.resourceType = filters.resourceType;
  if (filters.resourceId) query.resourceId = filters.resourceId;
  if (filters.ipAddress) query.ipAddress = new RegExp(filters.ipAddress, 'i');

  if (filters.startDate || filters.endDate) {
    query.timestamp = {};
    if (filters.startDate) query.timestamp.$gte = filters.startDate;
    if (filters.endDate) query.timestamp.$lte = filters.endDate;
  }

  return this.find(query)
    .populate('userId', 'email profile')
    .sort({ timestamp: -1 });
};

// Instance methods
auditLogSchema.methods.softDelete = function() {
  this.deletedAt = new Date();
};

auditLogSchema.methods.restore = function() {
  this.deletedAt = null;
};

// Virtual for formatted timestamp
auditLogSchema.virtual('formattedTimestamp').get(function() {
  return this.timestamp.toISOString();
});

// Ensure virtual fields are serialized
auditLogSchema.set('toJSON', { virtuals: true });

// Create and export the model
const AuditLog: Model<IAuditLog> = mongoose.model<IAuditLog>('AuditLog', auditLogSchema);

export default AuditLog;
