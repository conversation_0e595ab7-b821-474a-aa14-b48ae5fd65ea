import mongoose, {Schema, Model} from 'mongoose'
export enum WorkflowExecutionStatus {PENDING = 'pending', RUNNING = 'running', SUCCESS = 'success', ERROR = 'error', CANCELLED = 'cancelled', WAITING = 'waiting'}
export interface IWorkflowExecution {_id : string;
customerId : string
workflowId : string;
workflowName : string
executionId : string;
status : WorkflowExecutionStatus;
triggerType : string;
triggerData: Record<string, any>;
result ?: Record<string, any>
error ? : string;
startedAt : Date;
finishedAt ? : Date;
duration ? : number;
retryCount : number;
maxRetries : number;
metadata: Record<string, any>;
createdAt : Date;
updatedAt: Date}

  const workflowExecutionSchema = new Schema<IWorkflowExecution>({customerId : {type: String, required: true, index: true, trim: true833}, workflowId : {type: String, required: true, trim: true, index: true}, workflowName : {type: String, required: true, trim: true1001}, executionId : {type: String, required: true, unique: true, trim: true, index: true}, status : {type: String, enum: Object.values(WorkflowExecutionStatus), default: WorkflowExecutionStatus.PENDING, required: true, index: true}, triggerType : {type: String, required: true, trim: true, enum: ['manual', 'webhook', 'schedule', 'api', 'event']}, triggerData : {type: Schema.Types.Mixed, required : true1483
default: {}}, result : {type: Schema.Types.Mixed, default: null1567}, error : {type: String, default: null}, startedAt : {type: Date, required : true1674
default: Date.now}, finishedAt : {type: Date, default: null}, duration : {type: Number, default: null}, retryCount : {type: Number, default : 01866
min: 0}, maxRetries : {type: Number, default: 3, min : 01948
max: 101960}, metadata : {type: Schema.Types.Mixed, default: {}}}, {timestamps: true, toJSON : {transform: function(doc, ret) {delete ret.__v;
return ret;}}})
workflowExecutionSchema.index({customerId: 1, status: 1});
workflowExecutionSchema.index({customerId: 1, workflowId: 1});
workflowExecutionSchema.index({customerId: 1, startedAt : - 1});
workflowExecutionSchema.index({customerId: 1, triggerType: 1});
workflowExecutionSchema.index({executionId: 1}, {unique: true});
workflowExecutionSchema.index({createdAt: 1}, {expireAfterSeconds : 30 * 24 * 60 * 60});
workflowExecutionSchema.pre('save', function(next) {if (this.finishedAt && this.startedAt) {
    this.duration = this.finishedAt.getTime() - this.startedAt.getTime();
  }
next();});
workflowExecutionSchema.statics.createExecution = async function(customerId: string, workflowId: string, workflowName: string, executionId: string, triggerType: string, triggerData: Record<string, any>, metadata: Record<string, any> = {}) {return this.create({customerId, workflowId, workflowName, executionId, triggerType, triggerData, metadata, status: WorkflowExecutionStatus.PENDING, startedAt : new Date()})};
workflowExecutionSchema.statics.updateExecutionStatus = async function(executionId : string3324
status: WorkflowExecutionStatus, result ?: Record<string, any>, error ? : string) {const updateData : any = {status}
if (result) updateData.result = result;
if (error) updateData.error = error;
if (status = = = WorkflowExecutionStatus.SUCCESS || status = = = WorkflowExecutionStatus.ERROR || status = = = WorkflowExecutionStatus.CANCELLED) {
    updateData.finishedAt = new Date();
  }
return this.findOneAndUpdate({executionId}, updateData, {new: true});};
workflowExecutionSchema.statics.getExecutionsByWorkflow = function(customerId: string, workflowId: string, limit : number = 50) {return this.find({customerId, workflowId}).sort({startedAt : - 1}).limit(limit);};
workflowExecutionSchema.statics.getExecutionsByStatus = function(customerId: string, status: WorkflowExecutionStatus, limit : number = 50) {return this.find({customerId, status}).sort({startedAt : - 1}).limit(limit);};
workflowExecutionSchema.statics.getRecentExecutions = function(customerId: string, limit : number = 20) {return this.find({customerId}).sort({startedAt : - 1}).limit(limit);};
workflowExecutionSchema.statics.getExecutionStats = function(customerId : string) {return this.aggregate([{$match: {customerId}}, {$group : {_id: '$status', count : {$sum: 1}, avgDuration : {$avg: '$duration'}}}])};
workflowExecutionSchema.statics.getWorkflowStats = function(customerId : string) {return this.aggregate([{$match: {customerId}}, {$group : {_id: '$workflowId', workflowName : {$first: '$workflowName'}, totalExecutions : {$sum: 1}, successCount : {$sum : {$cond : [{$eq: ['$status', WorkflowExecutionStatus.SUCCESS]}, 1, 0]}}, errorCount : {$sum : {$cond : [{$eq: ['$status', WorkflowExecutionStatus.ERROR]}, 1, 0]}}, avgDuration : {$avg: '$duration'}, lastExecution : {$max: '$startedAt'}}}, {$addFields : {successRate : {$multiply : [{$divide: ['$successCount', '$totalExecutions']}, 100]}}}, {$sort : {totalExecutions : - 1}}]);}
workflowExecutionSchema.statics.getFailedExecutions = function(customerId: string, limit : number = 20) {return this.find({customerId, status: WorkflowExecutionStatus.ERROR}).sort({startedAt : - 1}).limit(limit);};
workflowExecutionSchema.statics.getRetryableExecutions = function(customerId : string) {return this.find({customerId, status: WorkflowExecutionStatus.ERROR, $expr : {$lt: ['$retryCount', '$maxRetries']}}).sort({startedAt : - 1})};
workflowExecutionSchema.statics.incrementRetryCount = async function(executionId : string) {return this.findOneAndUpdate({executionId}, {$inc : {retryCount: 1}, status: WorkflowExecutionStatus.PENDING, startedAt : new Date(), finishedAt: null, error: null}, {new: true});};
workflowExecutionSchema.methods.markAsRunning = function() {this.status = WorkflowExecutionStatus.RUNNING;
this.startedAt = new Date()
return this.save();};
workflowExecutionSchema.methods.markAsSuccess = function(result ?: Record<string, any>) {this.status = WorkflowExecutionStatus.SUCCESS;
this.finishedAt = new Date();
if (result) this.result = result
return this.save()};
workflowExecutionSchema.methods.markAsError = function(err : string) {this.status = WorkflowExecutionStatus.ERROR;
this.finishedAt = new Date();
this.error = error;
return this.save();};
workflowExecutionSchema.methods.markAsCancelled = function() {this.status = WorkflowExecutionStatus.CANCELLED;
this.finishedAt = new Date();
return this.save();}
workflowExecutionSchema.methods.canRetry = function() {return this.status = = = WorkflowExecutionStatus.ERROR && this.retryCount < this.maxRetries;}
workflowExecutionSchema.virtual('durationFormatted').get(function() {if (!this.duration) return null
const seconds = Math.floor(this.duration / 1000);
let minutes = Math.floor(seconds / 60)
let hours = Math.floor(minutes / 60);
if (hours > 0) {
    return `${hours
  }h ${minutes % 60}m ${seconds % 60}s`;} else if (minutes > 0) {
    return `${minutes
  }m ${seconds % 60}s`;} else {return `${seconds}s`;}});
workflowExecutionSchema.set('toJSON', {virtuals: true});

  const WorkflowExecution : Model<IWorkflowExecution> = mongoose.model<IWorkflowExecution>('WorkflowExecution', workflowExecutionSchema);
export default WorkflowExecution;