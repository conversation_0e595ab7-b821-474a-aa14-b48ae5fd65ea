import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { Ticket, User } from '@/types';

// n8n configuration
const N8N_BASE_URL = process.env.N8N_BASE_URL || 'http://localhost:5678';
const N8N_API_KEY = process.env.N8N_API_KEY || '';
const N8N_WEBHOOK_SECRET = process.env.N8N_WEBHOOK_SECRET || 'n8n-webhook-secret-key';

// Workflow IDs (these would be configured per tenant)
const WORKFLOW_IDS = {
  TICKET_CREATED: process.env.N8N_TICKET_CREATED_WORKFLOW || 'ticket-created-workflow',
  TICKET_UPDATED: process.env.N8N_TICKET_UPDATED_WORKFLOW || 'ticket-updated-workflow',
  TICKET_ASSIGNED: process.env.N8N_TICKET_ASSIGNED_WORKFLOW || 'ticket-assigned-workflow',
  TICKET_RESOLVED: process.env.N8N_TICKET_RESOLVED_WORKFLOW || 'ticket-resolved-workflow',
  USER_CREATED: process.env.N8N_USER_CREATED_WORKFLOW || 'user-created-workflow',
  DAILY_REPORT: process.env.N8N_DAILY_REPORT_WORKFLOW || 'daily-report-workflow',
} as const;

// Workflow execution data interface
interface WorkflowExecutionData {
  workflowId: string;
  data: Record<string, any>;
  customerId: string;
  userId?: string;
  metadata?: Record<string, any>;
}

// Workflow execution result interface
interface WorkflowExecutionResult {
  executionId: string;
  status: 'running' | 'success' | 'error' | 'waiting';
  data?: any;
  error?: string;
  startedAt: string;
  finishedAt?: string;
}

// n8n API client
class N8nApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: `${N8N_BASE_URL}/api/v1`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        ...(N8N_API_KEY && { 'X-N8N-API-KEY': N8N_API_KEY }),
      },
    });

    // Request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        console.log(`n8n API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('n8n API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        console.error('n8n API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Execute a workflow
   */
  async executeWorkflow(workflowId: string, data: Record<string, any>): Promise<WorkflowExecutionResult> {
    try {
      const response = await this.client.post(`/workflows/${workflowId}/execute`, {
        data,
      });

      return {
        executionId: response.data.executionId,
        status: response.data.status || 'running',
        data: response.data.data,
        startedAt: new Date().toISOString(),
      };
    } catch (error: any) {
      throw new Error(`Failed to execute workflow ${workflowId}: ${error.message}`);
    }
  }

  /**
   * Get workflow execution status
   */
  async getExecutionStatus(executionId: string): Promise<WorkflowExecutionResult> {
    try {
      const response = await this.client.get(`/executions/${executionId}`);
      
      return {
        executionId,
        status: response.data.status,
        data: response.data.data,
        error: response.data.error,
        startedAt: response.data.startedAt,
        finishedAt: response.data.finishedAt,
      };
    } catch (error: any) {
      throw new Error(`Failed to get execution status for ${executionId}: ${error.message}`);
    }
  }

  /**
   * Get workflow details
   */
  async getWorkflow(workflowId: string) {
    try {
      const response = await this.client.get(`/workflows/${workflowId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(`Failed to get workflow ${workflowId}: ${error.message}`);
    }
  }

  /**
   * List all workflows
   */
  async listWorkflows() {
    try {
      const response = await this.client.get('/workflows');
      return response.data;
    } catch (error: any) {
      throw new Error(`Failed to list workflows: ${error.message}`);
    }
  }

  /**
   * Trigger webhook workflow
   */
  async triggerWebhook(webhookPath: string, data: Record<string, any>, method: 'GET' | 'POST' = 'POST') {
    try {
      const webhookUrl = `${N8N_BASE_URL}/webhook/${webhookPath}`;
      
      const config: AxiosRequestConfig = {
        method,
        url: webhookUrl,
        headers: {
          'Content-Type': 'application/json',
          'X-Webhook-Secret': N8N_WEBHOOK_SECRET,
        },
        timeout: 30000,
      };

      if (method === 'POST') {
        config.data = data;
      } else {
        config.params = data;
      }

      const response = await axios(config);
      return response.data;
    } catch (error: any) {
      throw new Error(`Failed to trigger webhook ${webhookPath}: ${error.message}`);
    }
  }
}

// Main n8n service class
class N8nService {
  private apiClient: N8nApiClient;

  constructor() {
    this.apiClient = new N8nApiClient();
  }

  /**
   * Trigger ticket created workflow
   */
  async triggerTicketCreated(ticket: any, user: any): Promise<WorkflowExecutionResult> {
    const data = {
      ticket: {
        id: ticket._id,
        title: ticket.title,
        description: ticket.description,
        status: ticket.status,
        priority: ticket.priority,
        category: ticket.category,
        customerId: ticket.customerId,
        createdAt: ticket.createdAt,
      },
      user: {
        id: user._id,
        email: user.email,
        name: `${user.profile.firstName} ${user.profile.lastName}`,
        role: user.role,
      },
      metadata: {
        triggeredAt: new Date().toISOString(),
        source: 'flowbit-api',
      },
    };

    return this.apiClient.executeWorkflow(WORKFLOW_IDS.TICKET_CREATED, data);
  }

  /**
   * Trigger ticket updated workflow
   */
  async triggerTicketUpdated(ticket: any, user: any, changes: Record<string, any>): Promise<WorkflowExecutionResult> {
    const data = {
      ticket: {
        id: ticket._id,
        title: ticket.title,
        description: ticket.description,
        status: ticket.status,
        priority: ticket.priority,
        category: ticket.category,
        customerId: ticket.customerId,
        updatedAt: ticket.updatedAt,
      },
      user: {
        id: user._id,
        email: user.email,
        name: `${user.profile.firstName} ${user.profile.lastName}`,
        role: user.role,
      },
      changes,
      metadata: {
        triggeredAt: new Date().toISOString(),
        source: 'flowbit-api',
      },
    };

    return this.apiClient.executeWorkflow(WORKFLOW_IDS.TICKET_UPDATED, data);
  }

  /**
   * Trigger ticket assigned workflow
   */
  async triggerTicketAssigned(ticket: any, assignedUser: any, assignedBy: any): Promise<WorkflowExecutionResult> {
    const data = {
      ticket: {
        id: ticket._id,
        title: ticket.title,
        description: ticket.description,
        status: ticket.status,
        priority: ticket.priority,
        category: ticket.category,
        customerId: ticket.customerId,
      },
      assignedUser: {
        id: assignedUser._id,
        email: assignedUser.email,
        name: `${assignedUser.profile.firstName} ${assignedUser.profile.lastName}`,
        role: assignedUser.role,
      },
      assignedBy: {
        id: assignedBy._id,
        email: assignedBy.email,
        name: `${assignedBy.profile.firstName} ${assignedBy.profile.lastName}`,
        role: assignedBy.role,
      },
      metadata: {
        triggeredAt: new Date().toISOString(),
        source: 'flowbit-api',
      },
    };

    return this.apiClient.executeWorkflow(WORKFLOW_IDS.TICKET_ASSIGNED, data);
  }

  /**
   * Trigger ticket resolved workflow
   */
  async triggerTicketResolved(ticket: any, resolvedBy: any): Promise<WorkflowExecutionResult> {
    const data = {
      ticket: {
        id: ticket._id,
        title: ticket.title,
        description: ticket.description,
        status: ticket.status,
        priority: ticket.priority,
        category: ticket.category,
        customerId: ticket.customerId,
        resolvedAt: new Date().toISOString(),
      },
      resolvedBy: {
        id: resolvedBy._id,
        email: resolvedBy.email,
        name: `${resolvedBy.profile.firstName} ${resolvedBy.profile.lastName}`,
        role: resolvedBy.role,
      },
      metadata: {
        triggeredAt: new Date().toISOString(),
        source: 'flowbit-api',
      },
    };

    return this.apiClient.executeWorkflow(WORKFLOW_IDS.TICKET_RESOLVED, data);
  }

  /**
   * Trigger user created workflow
   */
  async triggerUserCreated(user: any, createdBy?: any): Promise<WorkflowExecutionResult> {
    const data = {
      user: {
        id: user._id,
        email: user.email,
        name: `${user.profile.firstName} ${user.profile.lastName}`,
        role: user.role,
        customerId: user.customerId,
        createdAt: user.createdAt,
      },
      createdBy: createdBy ? {
        id: createdBy._id,
        email: createdBy.email,
        name: `${createdBy.profile.firstName} ${createdBy.profile.lastName}`,
        role: createdBy.role,
      } : null,
      metadata: {
        triggeredAt: new Date().toISOString(),
        source: 'flowbit-api',
      },
    };

    return this.apiClient.executeWorkflow(WORKFLOW_IDS.USER_CREATED, data);
  }

  /**
   * Trigger daily report workflow
   */
  async triggerDailyReport(customerId: string, reportData: any): Promise<WorkflowExecutionResult> {
    const data = {
      customerId,
      reportData,
      reportDate: new Date().toISOString().split('T')[0],
      metadata: {
        triggeredAt: new Date().toISOString(),
        source: 'flowbit-api',
      },
    };

    return this.apiClient.executeWorkflow(WORKFLOW_IDS.DAILY_REPORT, data);
  }

  /**
   * Trigger custom webhook
   */
  async triggerCustomWebhook(webhookPath: string, data: Record<string, any>, method: 'GET' | 'POST' = 'POST') {
    return this.apiClient.triggerWebhook(webhookPath, data, method);
  }

  /**
   * Get execution status
   */
  async getExecutionStatus(executionId: string): Promise<WorkflowExecutionResult> {
    return this.apiClient.getExecutionStatus(executionId);
  }

  /**
   * Health check for n8n service
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.apiClient.listWorkflows();
      return true;
    } catch (error) {
      console.error('n8n health check failed:', error);
      return false;
    }
  }

  /**
   * Get workflow configuration
   */
  async getWorkflowConfig(workflowId: string) {
    return this.apiClient.getWorkflow(workflowId);
  }

  /**
   * List all available workflows
   */
  async listWorkflows() {
    return this.apiClient.listWorkflows();
  }
}

// Create and export n8n service instance
export const n8nService = new N8nService();

// Export workflow IDs for reference
export { WORKFLOW_IDS };
