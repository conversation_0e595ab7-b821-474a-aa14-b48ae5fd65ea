import fs from 'fs/promises';
import path from 'path';
import { Registry, TenantConfig, ScreenConfig, UserRole } from '@/types';

// Cache for registry data
let registryCache: Registry | null = null;
let lastModified: number = 0;

/**
 * Load registry from file system
 */
const loadRegistryFromFile = async (): Promise<Registry> => {
  try {
    const registryPath = path.join(process.cwd(), 'registry.json');
    const stats = await fs.stat(registryPath);
    
    // Check if file has been modified since last load
    if (registryCache && stats.mtimeMs <= lastModified) {
      return registryCache;
    }
    
    const registryData = await fs.readFile(registryPath, 'utf-8');
    const registry: Registry = JSON.parse(registryData);
    
    // Update cache
    registryCache = registry;
    lastModified = stats.mtimeMs;
    
    return registry;
  } catch (error) {
    console.error('Error loading registry:', error);
    throw new Error('Failed to load tenant registry');
  }
};

/**
 * Save registry to file system
 */
const saveRegistryToFile = async (registry: Registry): Promise<void> => {
  try {
    const registryPath = path.join(process.cwd(), 'registry.json');
    const registryData = JSON.stringify(registry, null, 2);
    
    await fs.writeFile(registryPath, registryData, 'utf-8');
    
    // Update cache
    registryCache = registry;
    lastModified = Date.now();
    
    console.log('Registry saved successfully');
  } catch (error) {
    console.error('Error saving registry:', error);
    throw new Error('Failed to save tenant registry');
  }
};

/**
 * Get all tenants
 */
export const getAllTenants = async (): Promise<Record<string, TenantConfig>> => {
  const registry = await loadRegistryFromFile();
  return registry.tenants;
};

/**
 * Get tenant configuration by ID
 */
export const getTenantConfig = async (customerId: string): Promise<TenantConfig | null> => {
  const registry = await loadRegistryFromFile();
  return registry.tenants[customerId] || null;
};

/**
 * Get screens for a tenant filtered by user role
 */
export const getTenantScreens = async (
  customerId: string, 
  userRole: UserRole
): Promise<ScreenConfig[]> => {
  const tenantConfig = await getTenantConfig(customerId);
  
  if (!tenantConfig) {
    return [];
  }
  
  // Filter screens based on user role permissions
  return tenantConfig.screens.filter(screen => 
    screen.permissions.includes(userRole)
  );
};

/**
 * Get screen configuration by ID for a tenant
 */
export const getTenantScreen = async (
  customerId: string, 
  screenId: string
): Promise<ScreenConfig | null> => {
  const tenantConfig = await getTenantConfig(customerId);
  
  if (!tenantConfig) {
    return null;
  }
  
  return tenantConfig.screens.find(screen => screen.id === screenId) || null;
};

/**
 * Check if user has access to a specific screen
 */
export const hasScreenAccess = async (
  customerId: string,
  screenId: string,
  userRole: UserRole
): Promise<boolean> => {
  const screen = await getTenantScreen(customerId, screenId);
  
  if (!screen) {
    return false;
  }
  
  return screen.permissions.includes(userRole);
};

/**
 * Get tenant features
 */
export const getTenantFeatures = async (customerId: string): Promise<Record<string, boolean>> => {
  const tenantConfig = await getTenantConfig(customerId);
  return tenantConfig?.features || {};
};

/**
 * Check if tenant has a specific feature enabled
 */
export const hasTenantFeature = async (
  customerId: string, 
  featureName: string
): Promise<boolean> => {
  const features = await getTenantFeatures(customerId);
  return features[featureName] === true;
};

/**
 * Get tenant settings
 */
export const getTenantSettings = async (customerId: string): Promise<Record<string, any>> => {
  const tenantConfig = await getTenantConfig(customerId);
  return tenantConfig?.settings || {};
};

/**
 * Get global settings
 */
export const getGlobalSettings = async (): Promise<Record<string, any>> => {
  const registry = await loadRegistryFromFile();
  return registry.globalSettings;
};

/**
 * Update tenant configuration (admin only)
 */
export const updateTenantConfig = async (
  customerId: string, 
  updates: Partial<TenantConfig>
): Promise<TenantConfig> => {
  const registry = await loadRegistryFromFile();
  
  if (!registry.tenants[customerId]) {
    throw new Error('Tenant not found');
  }
  
  // Merge updates with existing config
  registry.tenants[customerId] = {
    ...registry.tenants[customerId],
    ...updates
  };
  
  // Update last modified timestamp
  registry.lastUpdated = new Date().toISOString();
  
  await saveRegistryToFile(registry);
  
  return registry.tenants[customerId];
};

/**
 * Add new screen to tenant
 */
export const addTenantScreen = async (
  customerId: string,
  screen: ScreenConfig
): Promise<ScreenConfig[]> => {
  const registry = await loadRegistryFromFile();
  
  if (!registry.tenants[customerId]) {
    throw new Error('Tenant not found');
  }
  
  // Check if screen ID already exists
  const existingScreen = registry.tenants[customerId].screens.find(s => s.id === screen.id);
  if (existingScreen) {
    throw new Error('Screen ID already exists');
  }
  
  // Add new screen
  registry.tenants[customerId].screens.push(screen);
  registry.lastUpdated = new Date().toISOString();
  
  await saveRegistryToFile(registry);
  
  return registry.tenants[customerId].screens;
};

/**
 * Update tenant screen
 */
export const updateTenantScreen = async (
  customerId: string,
  screenId: string,
  updates: Partial<ScreenConfig>
): Promise<ScreenConfig | null> => {
  const registry = await loadRegistryFromFile();
  
  if (!registry.tenants[customerId]) {
    throw new Error('Tenant not found');
  }
  
  const screenIndex = registry.tenants[customerId].screens.findIndex(s => s.id === screenId);
  if (screenIndex === -1) {
    throw new Error('Screen not found');
  }
  
  // Update screen
  registry.tenants[customerId].screens[screenIndex] = {
    ...registry.tenants[customerId].screens[screenIndex],
    ...updates
  };
  
  registry.lastUpdated = new Date().toISOString();
  
  await saveRegistryToFile(registry);
  
  return registry.tenants[customerId].screens[screenIndex];
};

/**
 * Remove screen from tenant
 */
export const removeTenantScreen = async (
  customerId: string,
  screenId: string
): Promise<boolean> => {
  const registry = await loadRegistryFromFile();
  
  if (!registry.tenants[customerId]) {
    throw new Error('Tenant not found');
  }
  
  const screenIndex = registry.tenants[customerId].screens.findIndex(s => s.id === screenId);
  if (screenIndex === -1) {
    return false;
  }
  
  // Remove screen
  registry.tenants[customerId].screens.splice(screenIndex, 1);
  registry.lastUpdated = new Date().toISOString();
  
  await saveRegistryToFile(registry);
  
  return true;
};

/**
 * Create new tenant
 */
export const createTenant = async (
  customerId: string,
  tenantConfig: TenantConfig
): Promise<TenantConfig> => {
  const registry = await loadRegistryFromFile();
  
  if (registry.tenants[customerId]) {
    throw new Error('Tenant already exists');
  }
  
  // Add new tenant
  registry.tenants[customerId] = tenantConfig;
  registry.lastUpdated = new Date().toISOString();
  
  await saveRegistryToFile(registry);
  
  return tenantConfig;
};

/**
 * Delete tenant
 */
export const deleteTenant = async (customerId: string): Promise<boolean> => {
  const registry = await loadRegistryFromFile();
  
  if (!registry.tenants[customerId]) {
    return false;
  }
  
  // Remove tenant
  delete registry.tenants[customerId];
  registry.lastUpdated = new Date().toISOString();
  
  await saveRegistryToFile(registry);
  
  return true;
};

/**
 * Get registry statistics
 */
export const getRegistryStats = async () => {
  const registry = await loadRegistryFromFile();
  
  const tenantIds = Object.keys(registry.tenants);
  const totalScreens = tenantIds.reduce((total, tenantId) => {
    return total + registry.tenants[tenantId].screens.length;
  }, 0);
  
  return {
    totalTenants: tenantIds.length,
    totalScreens,
    version: registry.version,
    lastUpdated: registry.lastUpdated,
    tenants: tenantIds.map(tenantId => ({
      id: tenantId,
      name: registry.tenants[tenantId].name,
      screenCount: registry.tenants[tenantId].screens.length,
      theme: registry.tenants[tenantId].theme
    }))
  };
};

/**
 * Validate tenant configuration
 */
export const validateTenantConfig = (config: Partial<TenantConfig>): string[] => {
  const errors: string[] = [];
  
  if (config.name && typeof config.name !== 'string') {
    errors.push('Name must be a string');
  }
  
  if (config.theme && !['blue', 'green', 'red', 'purple', 'orange'].includes(config.theme)) {
    errors.push('Theme must be one of: blue, green, red, purple, orange');
  }
  
  if (config.screens) {
    config.screens.forEach((screen, index) => {
      if (!screen.id || typeof screen.id !== 'string') {
        errors.push(`Screen ${index}: ID is required and must be a string`);
      }
      
      if (!screen.name || typeof screen.name !== 'string') {
        errors.push(`Screen ${index}: Name is required and must be a string`);
      }
      
      if (!screen.url || typeof screen.url !== 'string') {
        errors.push(`Screen ${index}: URL is required and must be a string`);
      }
      
      if (!screen.permissions || !Array.isArray(screen.permissions)) {
        errors.push(`Screen ${index}: Permissions are required and must be an array`);
      }
    });
  }
  
  return errors;
};
