import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { ApiResponse } from '@/types';

/**
 * General API rate limiting
 */
export const apiRateLimit = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // 100 requests per window
  message: {
    success: false,
    error: 'Too many requests from this IP, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: Request, res: Response) => {
    const response: ApiResponse = {
      success: false,
      error: 'Rate limit exceeded. Please try again later.'
    };
    res.status(429).json(response);
  }
});

/**
 * Strict rate limiting for authentication endpoints
 */
export const authRateLimit = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.LOGIN_RATE_LIMIT_MAX || '5'), // 5 login attempts per window
  message: {
    success: false,
    error: 'Too many login attempts from this IP, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests
  handler: (req: Request, res: Response) => {
    const response: ApiResponse = {
      success: false,
      error: 'Too many login attempts. Please try again later.'
    };
    res.status(429).json(response);
  }
});

/**
 * Helmet security middleware configuration
 */
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "ws:", "wss:"],
      fontSrc: ["'self'", "data:"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for development
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

/**
 * CORS configuration
 */
export const corsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'];
    
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Webhook-Secret'
  ],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count']
};

/**
 * Request size limiting middleware
 */
export const requestSizeLimit = (req: Request, res: Response, next: NextFunction) => {
  const maxSize = process.env.MAX_REQUEST_SIZE || '10mb';
  const contentLength = req.get('content-length');
  
  if (contentLength) {
    const sizeInMB = parseInt(contentLength) / (1024 * 1024);
    const maxSizeInMB = parseInt(maxSize.replace('mb', ''));
    
    if (sizeInMB > maxSizeInMB) {
      const response: ApiResponse = {
        success: false,
        error: `Request size too large. Maximum allowed: ${maxSize}`
      };
      res.status(413).json(response);
      return;
    }
  }
  
  next();
};

/**
 * Request timeout middleware
 */
export const requestTimeout = (req: Request, res: Response, next: NextFunction) => {
  const timeout = parseInt(process.env.REQUEST_TIMEOUT_MS || '30000');
  
  req.setTimeout(timeout, () => {
    const response: ApiResponse = {
      success: false,
      error: 'Request timeout'
    };
    res.status(408).json(response);
  });
  
  next();
};

/**
 * IP whitelist middleware (for webhook endpoints)
 */
export const ipWhitelist = (allowedIPs: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || req.connection.remoteAddress;
    
    if (!clientIP || !allowedIPs.includes(clientIP)) {
      const response: ApiResponse = {
        success: false,
        error: 'Access denied: IP not whitelisted'
      };
      res.status(403).json(response);
      return;
    }
    
    next();
  };
};

/**
 * Webhook signature verification middleware
 */
export const verifyWebhookSignature = (secret: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const signature = req.get('X-Webhook-Secret');
    
    if (!signature || signature !== secret) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid webhook signature'
      };
      res.status(401).json(response);
      return;
    }
    
    next();
  };
};

/**
 * Content type validation middleware
 */
export const validateContentType = (allowedTypes: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentType = req.get('content-type');
    
    if (!contentType || !allowedTypes.some(type => contentType.includes(type))) {
      const response: ApiResponse = {
        success: false,
        error: `Invalid content type. Allowed: ${allowedTypes.join(', ')}`
      };
      res.status(415).json(response);
      return;
    }
    
    next();
  };
};

/**
 * API key validation middleware (for external integrations)
 */
export const validateApiKey = (req: Request, res: Response, next: NextFunction) => {
  const apiKey = req.get('X-API-Key');
  const validApiKeys = process.env.VALID_API_KEYS?.split(',') || [];
  
  if (!apiKey || !validApiKeys.includes(apiKey)) {
    const response: ApiResponse = {
      success: false,
      error: 'Invalid or missing API key'
    };
    res.status(401).json(response);
    return;
  }
  
  next();
};

/**
 * Request logging middleware
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const logData = {
      method: req.method,
      url: req.originalUrl,
      status: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    };
    
    // Log to console in development, use proper logger in production
    if (process.env.NODE_ENV === 'development') {
      console.log(`${logData.method} ${logData.url} ${logData.status} ${logData.duration}`);
    }
  });
  
  next();
};

/**
 * Error handling middleware
 */
export const errorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error('Error:', error);
  
  // Mongoose validation error
  if (error.name === 'ValidationError') {
    const errors = Object.values(error.errors).map((err: any) => ({
      field: err.path,
      message: err.message
    }));
    
    const response: ApiResponse = {
      success: false,
      error: 'Validation failed',
      errors
    };
    res.status(400).json(response);
    return;
  }
  
  // Mongoose duplicate key error
  if (error.code === 11000) {
    const field = Object.keys(error.keyValue)[0];
    const response: ApiResponse = {
      success: false,
      error: `Duplicate value for field: ${field}`
    };
    res.status(409).json(response);
    return;
  }
  
  // JWT errors
  if (error.name === 'JsonWebTokenError') {
    const response: ApiResponse = {
      success: false,
      error: 'Invalid token'
    };
    res.status(401).json(response);
    return;
  }
  
  // Default error response
  const response: ApiResponse = {
    success: false,
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : error.message
  };
  
  res.status(error.statusCode || 500).json(response);
};
