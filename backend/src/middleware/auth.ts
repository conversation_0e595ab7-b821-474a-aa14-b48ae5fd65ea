import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest, UserRole, ApiResponse } from '@/types';
import { verifyAccessToken, extractTokenFromHeader } from '@/utils/jwt';
import User from '@/models/User';
import AuditLog from '@/models/AuditLog';
import { AuditAction } from '@/types';

/**
 * Authentication middleware - verifies JWT token and adds user to request
 */
export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Extract token from Authorization header
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      const response: ApiResponse = {
        success: false,
        error: 'Access token required'
      };
      res.status(401).json(response);
      return;
    }

    // Verify token
    let decoded;
    try {
      decoded = verifyAccessToken(token);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Invalid token'
      };
      res.status(401).json(response);
      return;
    }

    // Check if user exists and is active
    const user = await User.findById(decoded.userId);
    if (!user || !user.isActive || user.deletedAt) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found or inactive'
      };
      res.status(401).json(response);
      return;
    }

    // Verify tenant consistency
    if (user.customerId !== decoded.customerId) {
      const response: ApiResponse = {
        success: false,
        error: 'Tenant mismatch'
      };
      res.status(401).json(response);
      return;
    }

    // Add user context to request
    (req as AuthenticatedRequest).user = {
      userId: decoded.userId,
      customerId: decoded.customerId,
      email: decoded.email,
      role: decoded.role
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Authentication failed'
    };
    res.status(500).json(response);
  }
};

/**
 * Authorization middleware - checks if user has required role
 */
export const authorize = (allowedRoles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const authReq = req as AuthenticatedRequest;
    
    if (!authReq.user) {
      const response: ApiResponse = {
        success: false,
        error: 'Authentication required'
      };
      res.status(401).json(response);
      return;
    }

    if (!allowedRoles.includes(authReq.user.role)) {
      const response: ApiResponse = {
        success: false,
        error: 'Insufficient permissions'
      };
      res.status(403).json(response);
      return;
    }

    next();
  };
};

/**
 * Admin-only authorization middleware
 */
export const requireAdmin = authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]);

/**
 * Super admin-only authorization middleware
 */
export const requireSuperAdmin = authorize([UserRole.SUPER_ADMIN]);

/**
 * User or admin authorization middleware
 */
export const requireUserOrAdmin = authorize([UserRole.USER, UserRole.ADMIN, UserRole.SUPER_ADMIN]);

/**
 * Tenant isolation middleware - ensures all queries are scoped to user's tenant
 */
export const tenantIsolation = (req: Request, res: Response, next: NextFunction): void => {
  const authReq = req as AuthenticatedRequest;
  
  if (!authReq.user) {
    const response: ApiResponse = {
      success: false,
      error: 'Authentication required for tenant isolation'
    };
    res.status(401).json(response);
    return;
  }

  // Add tenant context to request for use in controllers
  req.tenant = {
    customerId: authReq.user.customerId
  };

  next();
};

/**
 * Audit logging middleware - logs user actions
 */
export const auditLog = (action: AuditAction, resourceType: string) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    
    if (!authReq.user) {
      next();
      return;
    }

    try {
      // Get client IP and user agent
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent') || 'unknown';

      // Extract resource ID from request params if available
      const resourceId = req.params.id || req.body.id || undefined;

      // Create audit log entry
      await AuditLog.logAction(
        authReq.user.customerId,
        authReq.user.userId,
        action,
        resourceType,
        {
          method: req.method,
          url: req.originalUrl,
          body: req.method !== 'GET' ? req.body : undefined,
          query: req.query
        },
        resourceId,
        ipAddress,
        userAgent
      );
    } catch (error) {
      console.error('Audit logging error:', error);
      // Don't fail the request if audit logging fails
    }

    next();
  };
};

/**
 * Optional authentication middleware - adds user to request if token is present
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      next();
      return;
    }

    try {
      const decoded = verifyAccessToken(token);
      const user = await User.findById(decoded.userId);
      
      if (user && user.isActive && !user.deletedAt && user.customerId === decoded.customerId) {
        (req as AuthenticatedRequest).user = {
          userId: decoded.userId,
          customerId: decoded.customerId,
          email: decoded.email,
          role: decoded.role
        };
      }
    } catch (error) {
      // Ignore token errors for optional auth
    }

    next();
  } catch (error) {
    console.error('Optional authentication error:', error);
    next();
  }
};

/**
 * Resource ownership middleware - ensures user can only access their own resources
 */
export const requireResourceOwnership = (resourceUserIdField: string = 'userId') => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    
    if (!authReq.user) {
      const response: ApiResponse = {
        success: false,
        error: 'Authentication required'
      };
      res.status(401).json(response);
      return;
    }

    // Admins can access all resources within their tenant
    if (authReq.user.role === UserRole.ADMIN || authReq.user.role === UserRole.SUPER_ADMIN) {
      next();
      return;
    }

    // For regular users, check resource ownership
    const resourceUserId = req.body[resourceUserIdField] || req.params[resourceUserIdField];
    
    if (resourceUserId && resourceUserId !== authReq.user.userId) {
      const response: ApiResponse = {
        success: false,
        error: 'Access denied: insufficient permissions'
      };
      res.status(403).json(response);
      return;
    }

    next();
  };
};

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      tenant?: {
        customerId: string;
      };
    }
  }
}
