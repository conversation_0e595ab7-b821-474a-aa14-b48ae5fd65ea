import { Request, Response, NextFunction } from 'express';
import mongoose from 'mongoose';
import { AuthenticatedRequest, ApiResponse } from '@/types';

/**
 * Tenant isolation middleware that automatically injects customerId into all database queries
 */
export const tenantIsolationMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const authReq = req as AuthenticatedRequest;
  
  if (!authReq.user) {
    const response: ApiResponse = {
      success: false,
      error: 'Authentication required for tenant isolation'
    };
    res.status(401).json(response);
    return;
  }

  // Add tenant context to request
  req.tenant = {
    customerId: authReq.user.customerId
  };

  next();
};

/**
 * Mongoose plugin to automatically add tenant filtering to all queries
 */
export const tenantPlugin = function(schema: mongoose.Schema) {
  // Add customerId field if it doesn't exist
  if (!schema.paths.customerId) {
    schema.add({
      customerId: {
        type: String,
        required: true,
        index: true
      }
    });
  }

  // Pre-save middleware to ensure customerId is set
  schema.pre('save', function(next) {
    if (!this.customerId && this.constructor.currentTenant) {
      this.customerId = this.constructor.currentTenant;
    }
    next();
  });

  // Pre-find middleware to automatically filter by tenant
  schema.pre(/^find/, function() {
    const query = this.getQuery();
    
    // Skip tenant filtering if explicitly disabled
    if (this.getOptions().skipTenantFilter === true) {
      return;
    }

    // Skip if customerId is already in the query
    if (query.customerId) {
      return;
    }

    // Get current tenant from the model or global context
    const currentTenant = this.model.currentTenant || getCurrentTenant();
    
    if (currentTenant) {
      this.where({ customerId: currentTenant });
    }
  });

  // Pre-aggregate middleware for tenant filtering
  schema.pre('aggregate', function() {
    const pipeline = this.pipeline();
    
    // Skip tenant filtering if explicitly disabled
    if (this.options.skipTenantFilter === true) {
      return;
    }

    // Check if $match stage already includes customerId
    const matchStage = pipeline.find(stage => stage.$match);
    if (matchStage && matchStage.$match.customerId) {
      return;
    }

    // Get current tenant
    const currentTenant = this.model.currentTenant || getCurrentTenant();
    
    if (currentTenant) {
      // Add tenant filter as the first stage
      pipeline.unshift({ $match: { customerId: currentTenant } });
    }
  });

  // Pre-update middleware to prevent cross-tenant updates
  schema.pre(/^update/, function() {
    const query = this.getQuery();
    
    // Skip tenant filtering if explicitly disabled
    if (this.getOptions().skipTenantFilter === true) {
      return;
    }

    // Skip if customerId is already in the query
    if (query.customerId) {
      return;
    }

    // Get current tenant
    const currentTenant = this.model.currentTenant || getCurrentTenant();
    
    if (currentTenant) {
      this.where({ customerId: currentTenant });
    }
  });

  // Pre-delete middleware to prevent cross-tenant deletions
  schema.pre(/^delete/, function() {
    const query = this.getQuery();
    
    // Skip tenant filtering if explicitly disabled
    if (this.getOptions().skipTenantFilter === true) {
      return;
    }

    // Skip if customerId is already in the query
    if (query.customerId) {
      return;
    }

    // Get current tenant
    const currentTenant = this.model.currentTenant || getCurrentTenant();
    
    if (currentTenant) {
      this.where({ customerId: currentTenant });
    }
  });

  // Static method to set current tenant for the model
  schema.statics.setCurrentTenant = function(customerId: string) {
    this.currentTenant = customerId;
  };

  // Static method to clear current tenant
  schema.statics.clearCurrentTenant = function() {
    this.currentTenant = null;
  };

  // Static method to execute query without tenant filtering
  schema.statics.withoutTenantFilter = function() {
    return this.find({}, null, { skipTenantFilter: true });
  };

  // Static method to find across all tenants (admin only)
  schema.statics.findAcrossAllTenants = function(query = {}) {
    return this.find(query, null, { skipTenantFilter: true });
  };

  // Instance method to check if document belongs to current tenant
  schema.methods.belongsToCurrentTenant = function() {
    const currentTenant = this.constructor.currentTenant || getCurrentTenant();
    return this.customerId === currentTenant;
  };
};

// Global tenant context storage
let currentTenantContext: string | null = null;

/**
 * Set global tenant context
 */
export const setCurrentTenant = (customerId: string): void => {
  currentTenantContext = customerId;
};

/**
 * Get current tenant context
 */
export const getCurrentTenant = (): string | null => {
  return currentTenantContext;
};

/**
 * Clear current tenant context
 */
export const clearCurrentTenant = (): void => {
  currentTenantContext = null;
};

/**
 * Execute function with tenant context
 */
export const withTenant = async <T>(customerId: string, fn: () => Promise<T>): Promise<T> => {
  const previousTenant = getCurrentTenant();
  setCurrentTenant(customerId);
  
  try {
    return await fn();
  } finally {
    if (previousTenant) {
      setCurrentTenant(previousTenant);
    } else {
      clearCurrentTenant();
    }
  }
};

/**
 * Middleware to set tenant context from authenticated user
 */
export const setTenantContext = (req: Request, res: Response, next: NextFunction): void => {
  const authReq = req as AuthenticatedRequest;
  
  if (authReq.user && authReq.user.customerId) {
    setCurrentTenant(authReq.user.customerId);
  }
  
  next();
};

/**
 * Middleware to clear tenant context after request
 */
export const clearTenantContext = (req: Request, res: Response, next: NextFunction): void => {
  res.on('finish', () => {
    clearCurrentTenant();
  });
  
  next();
};

/**
 * Validate tenant access for specific resource
 */
export const validateTenantAccess = async (
  model: any,
  resourceId: string,
  customerId: string
): Promise<boolean> => {
  try {
    const resource = await model.findOne({
      _id: resourceId,
      customerId: customerId
    }, null, { skipTenantFilter: true });
    
    return !!resource;
  } catch (error) {
    return false;
  }
};

/**
 * Get tenant statistics
 */
export const getTenantStats = async (customerId: string) => {
  const User = mongoose.model('User');
  const Ticket = mongoose.model('Ticket');
  const AuditLog = mongoose.model('AuditLog');

  const [userCount, ticketCount, auditLogCount] = await Promise.all([
    User.countDocuments({ customerId, deletedAt: null }),
    Ticket.countDocuments({ customerId, deletedAt: null }),
    AuditLog.countDocuments({ customerId, deletedAt: null })
  ]);

  return {
    users: userCount,
    tickets: ticketCount,
    auditLogs: auditLogCount
  };
};

/**
 * Tenant data cleanup utility (for tenant deletion)
 */
export const cleanupTenantData = async (customerId: string): Promise<void> => {
  const User = mongoose.model('User');
  const Ticket = mongoose.model('Ticket');
  const AuditLog = mongoose.model('AuditLog');

  // Soft delete all tenant data
  await Promise.all([
    User.updateMany(
      { customerId },
      { deletedAt: new Date() },
      { skipTenantFilter: true }
    ),
    Ticket.updateMany(
      { customerId },
      { deletedAt: new Date() },
      { skipTenantFilter: true }
    ),
    AuditLog.updateMany(
      { customerId },
      { deletedAt: new Date() },
      { skipTenantFilter: true }
    )
  ]);
};

/**
 * Tenant data export utility
 */
export const exportTenantData = async (customerId: string) => {
  const User = mongoose.model('User');
  const Ticket = mongoose.model('Ticket');
  const AuditLog = mongoose.model('AuditLog');

  const [users, tickets, auditLogs] = await Promise.all([
    User.find({ customerId, deletedAt: null }, null, { skipTenantFilter: true }).lean(),
    Ticket.find({ customerId, deletedAt: null }, null, { skipTenantFilter: true }).lean(),
    AuditLog.find({ customerId, deletedAt: null }, null, { skipTenantFilter: true }).lean()
  ]);

  return {
    customerId,
    exportDate: new Date().toISOString(),
    data: {
      users,
      tickets,
      auditLogs
    }
  };
};
