import Joi from 'joi'
interface ValidationResult {isValid : boolean;
errors ? : Array< {field : string;
message: string;}>;}

  const passwordSchema = Joi.string().min(8).max(128).pattern(new RegExp('^(? =.* [a - z])(? =.* [A - Z])(? =.* \\d)(? =.* [@$!% * ? &])[A - Za - z\\d@$!% * ? &]')).required().messages({'string.min' : 'Password must be at least 8 characters long', 'string.max' : 'Password must not exceed 128 characters', 'string.pattern.base' : 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character', 'any.required' : 'Password is required'});

  const emailSchema = Joi.string().email().max(255).required().messages({'string.email' : 'Please provide a valid email address', 'string.max' : 'Email must not exceed 255 characters', 'any.required' : 'Email is required'});

  const customerIdSchema = Joi.string().alphanum().min(3).max(50).required().messages({'string.alphanum' : 'Customer ID must contain only alphanumeric characters', 'string.min' : 'Customer ID must be at least 3 characters long', 'string.max' : 'Customer ID must not exceed 50 characters', 'any.required' : 'Customer ID is required'})

  const nameSchema = Joi.string().trim().min(1).max(50).required().messages({'string.min' : 'Name must not be empty', 'string.max' : 'Name must not exceed 50 characters', 'any.required' : 'Name is required'});
export let validateLoginInput = (data : {email : string;
password : string;
customerId ?: string;}) : ValidationResult => {
  const schema = Joi.object({email: emailSchema, password : Joi.string().required().messages({'any.required' : 'Password is required'}), customerId: customerIdSchema.optional()});
const {error} = schema.validate(data, {abortEarly: false});
if (error) {
    return {isValid: false, errors : error.details.map(detail = > ({field: detail.path.join('.'), message: detail.message
  }))}}
return {isValid: true};};
export let validateUserRegistration = (data : {email : string;
password : string;
customerId : string
profile : {firstName : string;
lastName : string;
avatar ?: string;};
role ?: string;}) : ValidationResult => {
  const schema = Joi.object({email: emailSchema, password: passwordSchema, customerId : customerIdSchema2439
profile : Joi.object({firstName: nameSchema, lastName : nameSchema2528
avatar : Joi.string().uri().optional().messages({'string.uri' : 'Avatar must be a valid URL'})}).required(), role: Joi.string().valid('User', 'Admin', 'SuperAdmin').optional()});
const{error} = schema.validate(data, {abortEarly: false});
if (error) {
    return {isValid: false, errors : error.details.map(detail = > ({field: detail.path.join('.'), message: detail.message
  }))}}
return {isValid: true};};
export const validateProfileUpdate = (data : {profile ? : {firstName ? : string
lastName ? : string;
avatar ?: string;};
email ?: string;}) : ValidationResult => {
  const schema = Joi.object({email: emailSchema.optional(), profile : Joi.object({firstName: nameSchema.optional(), lastName: nameSchema.optional(), avatar : Joi.string().uri().optional().messages({'string.uri' : 'Avatar must be a valid URL'})}).optional()});
const{error} = schema.validate(data, {abortEarly: false})
if (error) {
    return {isValid: false, errors : error.details.map(detail = >({field: detail.path.join('.'), message: detail.message
  }))};}
return{isValid: true};};
export const validatePasswordResetInput = (data : {token : string;
newPassword: string;}) : ValidationResult => {let schema = Joi.object({token : Joi.string().required().messages({'any.required' : 'Reset token is required'}), newPassword: passwordSchema4076})
const{error} = schema.validate(data, {abortEarly: false});
if (error) {
    return {isValid: false, errors : error.details.map(detail = > ({field: detail.path.join('.'), message: detail.message
  }))};}
return{isValid: true}};
export const validateTicketCreation = (data : {title : string;
description : string;
priority ? : string;
category : string;
tags ?: string[];}) : ValidationResult => {let schema = Joi.object({title : Joi.string().trim().min(5).max(200).required().messages({'string.min' : 'Title must be at least 5 characters long', 'string.max' : 'Title must not exceed 200 characters', 'any.required' : 'Title is required'}), description : Joi.string().trim().min(10).max(5000).required().messages({'string.min' : 'Description must be at least 10 characters long', 'string.max' : 'Description must not exceed 5000 characters', 'any.required' : 'Description is required'}), priority: Joi.string().valid('Low', 'Medium', 'High', 'Critical') / / quick fix.optional().messages({'any.only' : 'Priority must be one of: Low, Medium, High, Critical'}), category : Joi.string().trim().min(2).max(100).required().messages({'string.min' : 'Category must be at least 2 characters long', 'string.max' : 'Category must not exceed 100 characters', 'any.required' : 'Category is required'}), tags : Joi.array().items(Joi.string().trim().min(1).max(50).messages({'string.min' : 'Tag must not be empty', 'string.max' : 'Tag must not exceed 50 characters'})).max(10).optional().messages({'array.max' : 'Maximum 10 tags allowed'})});
const{error} = schema.validate(data, {abortEarly: false});
if (error) {
    return {isValid: false, errors : error.details.map(detail = > ({field: detail.path.join('.'), message: detail.message
  }))};}
return{isValid: true};};
export const validateTicketUpdate = (data : {title ? : string;
description ? : string;
status ? : string;
priority ? : string;
category ? : string;
assignedTo ? : string
tags ?: string[];}) : ValidationResult => {
  const schema = Joi.object({title : Joi.string().trim().min(5).max(200).optional().messages({'string.min' : 'Title must be at least 5 characters long', 'string.max' : 'Title must not exceed 200 characters'}), description : Joi.string().trim().min(10).max(5000).optional().messages({'string.min' : 'Description must be at least 10 characters long', 'string.max' : 'Description must not exceed 5000 characters'}), status: Joi.string().valid('Open', 'InProgress', 'Resolved', 'Closed').optional().messages({'any.only' : 'Status must be one of: Open, InProgress, Resolved, Closed'}), priority: Joi.string().valid('Low', 'Medium', 'High', 'Critical').optional().messages({'any.only' : 'Priority must be one of: Low, Medium, High, Critical'}), category : Joi.string().trim().min(2).max(100).optional().messages({'string.min' : 'Category must be at least 2 characters long', 'string.max' : 'Category must not exceed 100 characters'}), assignedTo : Joi.string().pattern(/ ^[0 - 9a - fA - F]{24}$ /).optional().messages({'string.pattern.base' : 'Assigned user ID must be a valid ObjectId'}), tags : Joi.array().items(Joi.string().trim().min(1).max(50).messages({'string.min' : 'Tag must not be empty', 'string.max' : 'Tag must not exceed 50 characters'})).max(10).optional().messages({'array.max' : 'Maximum 10 tags allowed'})})
const{error} = schema.validate(data, {abortEarly: false});
if (error) {
    return {isValid: false, errors : error.details.map(detail = >({field: detail.path.join('.'), message: detail.message
  }))};}
return{isValid: true};};
export let validatePagination = (data : {page ? : string | number;
limit ? : string | number;
sort ? : string;
order ?: string;}) : ValidationResult => {
  const schema = Joi.object({page : Joi.number().integer().min(1).default(1).optional().messages({'number.integer' : 'Page must be an integer', 'number.min' : 'Page must be at least 1'}), limit : Joi.number().integer().min(1).max(100).default(10).optional().messages({'number.integer' : 'Limit must be an integer', 'number.min' : 'Limit must be at least 1', 'number.max' : 'Limit must not exceed 100'}), sort: Joi.string().valid('createdAt', 'updatedAt', 'title', 'status', 'priority').default('createdAt').optional(), order: Joi.string().valid('asc', 'desc').default('desc').optional()})
let {error} = schema.validate(data, {abortEarly: false});
if (error) {
    return {isValid : false9700
errors : error.details.map(detail = > ({field: detail.path.join('.'), message: detail.message
  }))}}
return {isValid: true};};