import mongoose from 'mongoose';
import { DatabaseConfig } from '@/types';
import { tenantPlugin } from '@/middleware/tenant';

// Database configuration
const getDatabaseConfig = (): DatabaseConfig => {
  const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/flowbit';
  
  return {
    uri,
    options: {
      maxPoolSize: parseInt(process.env.DB_CONNECTION_POOL_SIZE || '10'),
      serverSelectionTimeoutMS: parseInt(process.env.DB_SERVER_SELECTION_TIMEOUT_MS || '5000'),
      socketTimeoutMS: parseInt(process.env.DB_SOCKET_TIMEOUT_MS || '45000'),
      family: 4 // Use IPv4, skip trying IPv6
    }
  };
};

/**
 * Connect to MongoDB with proper configuration
 */
export const connectDatabase = async (): Promise<void> => {
  try {
    const config = getDatabaseConfig();
    
    // Set mongoose options
    mongoose.set('strictQuery', true);
    
    // Add tenant plugin globally to all schemas
    mongoose.plugin(tenantPlugin);
    
    // Connect to MongoDB
    await mongoose.connect(config.uri, config.options);
    
    console.log('✅ Connected to MongoDB successfully');
    
    // Set up connection event listeners
    mongoose.connection.on('error', (error) => {
      console.error('❌ MongoDB connection error:', error);
    });
    
    mongoose.connection.on('disconnected', () => {
      console.warn('⚠️ MongoDB disconnected');
    });
    
    mongoose.connection.on('reconnected', () => {
      console.log('🔄 MongoDB reconnected');
    });
    
    // Graceful shutdown
    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      console.log('📴 MongoDB connection closed through app termination');
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
};

/**
 * Disconnect from MongoDB
 */
export const disconnectDatabase = async (): Promise<void> => {
  try {
    await mongoose.connection.close();
    console.log('📴 Disconnected from MongoDB');
  } catch (error) {
    console.error('❌ Error disconnecting from MongoDB:', error);
  }
};

/**
 * Check database connection health
 */
export const checkDatabaseHealth = async (): Promise<{
  status: 'healthy' | 'unhealthy';
  details: any;
}> => {
  try {
    const state = mongoose.connection.readyState;
    const stateMap = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };
    
    if (state === 1) {
      // Test database operation
      await mongoose.connection.db.admin().ping();
      
      return {
        status: 'healthy',
        details: {
          state: stateMap[state as keyof typeof stateMap],
          host: mongoose.connection.host,
          port: mongoose.connection.port,
          name: mongoose.connection.name
        }
      };
    } else {
      return {
        status: 'unhealthy',
        details: {
          state: stateMap[state as keyof typeof stateMap],
          error: 'Database not connected'
        }
      };
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
};

/**
 * Create database indexes for performance
 */
export const createDatabaseIndexes = async (): Promise<void> => {
  try {
    const db = mongoose.connection.db;
    
    // Users collection indexes
    await db.collection('users').createIndexes([
      { key: { customerId: 1 }, name: 'customerId_1' },
      { key: { email: 1, customerId: 1 }, name: 'email_customerId_unique', unique: true },
      { key: { customerId: 1, deletedAt: 1 }, name: 'customerId_deletedAt_1' },
      { key: { customerId: 1, role: 1 }, name: 'customerId_role_1' },
      { key: { customerId: 1, isActive: 1 }, name: 'customerId_isActive_1' },
      { key: { customerId: 1, lastLogin: -1 }, name: 'customerId_lastLogin_-1' }
    ]);
    
    // Tickets collection indexes
    await db.collection('tickets').createIndexes([
      { key: { customerId: 1 }, name: 'customerId_1' },
      { key: { customerId: 1, userId: 1 }, name: 'customerId_userId_1' },
      { key: { customerId: 1, status: 1 }, name: 'customerId_status_1' },
      { key: { customerId: 1, priority: 1 }, name: 'customerId_priority_1' },
      { key: { customerId: 1, category: 1 }, name: 'customerId_category_1' },
      { key: { customerId: 1, assignedTo: 1 }, name: 'customerId_assignedTo_1' },
      { key: { customerId: 1, createdAt: -1 }, name: 'customerId_createdAt_-1' },
      { key: { customerId: 1, updatedAt: -1 }, name: 'customerId_updatedAt_-1' },
      { key: { customerId: 1, deletedAt: 1 }, name: 'customerId_deletedAt_1' },
      { key: { title: 'text', description: 'text', category: 'text' }, name: 'text_search' }
    ]);
    
    // Audit logs collection indexes
    await db.collection('auditlogs').createIndexes([
      { key: { customerId: 1 }, name: 'customerId_1' },
      { key: { customerId: 1, userId: 1 }, name: 'customerId_userId_1' },
      { key: { customerId: 1, action: 1 }, name: 'customerId_action_1' },
      { key: { customerId: 1, resourceType: 1 }, name: 'customerId_resourceType_1' },
      { key: { customerId: 1, resourceId: 1 }, name: 'customerId_resourceId_1' },
      { key: { customerId: 1, timestamp: -1 }, name: 'customerId_timestamp_-1' },
      { key: { customerId: 1, timestamp: 1 }, name: 'customerId_timestamp_1' },
      { key: { customerId: 1, deletedAt: 1 }, name: 'customerId_deletedAt_1' },
      { key: { timestamp: 1 }, name: 'timestamp_ttl', expireAfterSeconds: 90 * 24 * 60 * 60 } // 90 days TTL
    ]);
    
    console.log('✅ Database indexes created successfully');
  } catch (error) {
    console.error('❌ Error creating database indexes:', error);
  }
};

/**
 * Database migration utility
 */
export const runDatabaseMigrations = async (): Promise<void> => {
  try {
    console.log('🔄 Running database migrations...');
    
    // Create indexes
    await createDatabaseIndexes();
    
    // Add any future migrations here
    
    console.log('✅ Database migrations completed successfully');
  } catch (error) {
    console.error('❌ Database migration failed:', error);
    throw error;
  }
};

/**
 * Database cleanup utility
 */
export const cleanupDatabase = async (): Promise<void> => {
  try {
    const db = mongoose.connection.db;
    
    // Clean up expired audit logs (older than 90 days)
    const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
    await db.collection('auditlogs').deleteMany({
      timestamp: { $lt: ninetyDaysAgo }
    });
    
    // Clean up soft-deleted records older than 30 days
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    
    await Promise.all([
      db.collection('users').deleteMany({
        deletedAt: { $lt: thirtyDaysAgo, $ne: null }
      }),
      db.collection('tickets').deleteMany({
        deletedAt: { $lt: thirtyDaysAgo, $ne: null }
      })
    ]);
    
    console.log('✅ Database cleanup completed');
  } catch (error) {
    console.error('❌ Database cleanup failed:', error);
  }
};

/**
 * Get database statistics
 */
export const getDatabaseStats = async () => {
  try {
    const db = mongoose.connection.db;
    
    const [usersStats, ticketsStats, auditLogsStats] = await Promise.all([
      db.collection('users').stats(),
      db.collection('tickets').stats(),
      db.collection('auditlogs').stats()
    ]);
    
    return {
      collections: {
        users: {
          count: usersStats.count,
          size: usersStats.size,
          avgObjSize: usersStats.avgObjSize,
          indexes: usersStats.nindexes
        },
        tickets: {
          count: ticketsStats.count,
          size: ticketsStats.size,
          avgObjSize: ticketsStats.avgObjSize,
          indexes: ticketsStats.nindexes
        },
        auditLogs: {
          count: auditLogsStats.count,
          size: auditLogsStats.size,
          avgObjSize: auditLogsStats.avgObjSize,
          indexes: auditLogsStats.nindexes
        }
      },
      database: {
        name: db.databaseName,
        collections: await db.listCollections().toArray()
      }
    };
  } catch (error) {
    console.error('❌ Error getting database stats:', error);
    return null;
  }
};

/**
 * Backup database utility
 */
export const backupDatabase = async (customerId?: string) => {
  try {
    const db = mongoose.connection.db;
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    let backupData: any = {
      timestamp,
      version: '1.0.0'
    };
    
    if (customerId) {
      // Backup specific tenant data
      const [users, tickets, auditLogs] = await Promise.all([
        db.collection('users').find({ customerId, deletedAt: null }).toArray(),
        db.collection('tickets').find({ customerId, deletedAt: null }).toArray(),
        db.collection('auditlogs').find({ customerId, deletedAt: null }).toArray()
      ]);
      
      backupData = {
        ...backupData,
        customerId,
        data: { users, tickets, auditLogs }
      };
    } else {
      // Backup all data
      const [users, tickets, auditLogs] = await Promise.all([
        db.collection('users').find({ deletedAt: null }).toArray(),
        db.collection('tickets').find({ deletedAt: null }).toArray(),
        db.collection('auditlogs').find({ deletedAt: null }).toArray()
      ]);
      
      backupData = {
        ...backupData,
        data: { users, tickets, auditLogs }
      };
    }
    
    return backupData;
  } catch (error) {
    console.error('❌ Database backup failed:', error);
    throw error;
  }
};
