import { Router } from 'express';
import { authenticate, requireAdmin, auditLog } from '@/middleware/auth';
import { tenantIsolationMiddleware } from '@/middleware/tenant';
import { AuditAction } from '@/types';
import {
  getWorkflowExecutions,
  getWorkflowExecution,
  triggerWorkflow,
  retryWorkflowExecution,
  cancelWorkflowExecution,
  getWorkflowStats,
  getAvailableWorkflows,
  testN8nConnection
} from '@/controllers/workflowController';

const router = Router();

// All workflow routes require authentication and tenant isolation
router.use(authenticate);
router.use(tenantIsolationMiddleware);

/**
 * @route   GET /api/workflows/executions
 * @desc    Get workflow executions for tenant
 * @access  Admin
 */
router.get('/executions', 
  requireAdmin,
  auditLog(AuditAction.ADMIN_ACCESS, 'WorkflowExecution'),
  getWorkflowExecutions
);

/**
 * @route   GET /api/workflows/executions/:executionId
 * @desc    Get workflow execution by ID
 * @access  Admin
 */
router.get('/executions/:executionId', 
  requireAdmin,
  auditLog(AuditAction.ADMIN_ACCESS, 'WorkflowExecution'),
  getWorkflowExecution
);

/**
 * @route   POST /api/workflows/trigger
 * @desc    Trigger workflow manually
 * @access  Admin
 */
router.post('/trigger', 
  requireAdmin,
  auditLog(AuditAction.WORKFLOW_TRIGGER, 'Workflow'),
  triggerWorkflow
);

/**
 * @route   POST /api/workflows/executions/:executionId/retry
 * @desc    Retry failed workflow execution
 * @access  Admin
 */
router.post('/executions/:executionId/retry', 
  requireAdmin,
  auditLog(AuditAction.WORKFLOW_RETRY, 'WorkflowExecution'),
  retryWorkflowExecution
);

/**
 * @route   POST /api/workflows/executions/:executionId/cancel
 * @desc    Cancel running workflow execution
 * @access  Admin
 */
router.post('/executions/:executionId/cancel', 
  requireAdmin,
  auditLog(AuditAction.WORKFLOW_CANCEL, 'WorkflowExecution'),
  cancelWorkflowExecution
);

/**
 * @route   GET /api/workflows/stats
 * @desc    Get workflow statistics
 * @access  Admin
 */
router.get('/stats', 
  requireAdmin,
  auditLog(AuditAction.ADMIN_ACCESS, 'WorkflowStats'),
  getWorkflowStats
);

/**
 * @route   GET /api/workflows/available
 * @desc    Get available workflows
 * @access  Admin
 */
router.get('/available', 
  requireAdmin,
  getAvailableWorkflows
);

/**
 * @route   GET /api/workflows/test-connection
 * @desc    Test n8n connection
 * @access  Admin
 */
router.get('/test-connection', 
  requireAdmin,
  testN8nConnection
);

export default router;
