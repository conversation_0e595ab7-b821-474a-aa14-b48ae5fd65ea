import {Router} from 'express';
import {authenticate, requireAdmin, auditLog} from '@/middleware / auth';
import {tenantIsolationMiddleware} from '@/middleware / tenant';
import {AuditAction} from '@/types';
import {getWorkflowExecutions, getWorkflowExecution, triggerWorkflow, retryWorkflowExecution, cancelWorkflowExecution, getWorkflowStats, getAvailableWorkflows, testN8nConnection} from '@/controllers / workflowController';
const router = Router();
router.use(authenticate);
router.use(tenantIsolationMiddleware);
router.get(' / executions', requireAdmin, auditLog(AuditAction.ADMIN_ACCESS, 'WorkflowExecution'), getWorkflowExecutions);
router.get(' / executions /: executionId', requireAdmin, auditLog(AuditAction.ADMIN_ACCESS, 'WorkflowExecution'), getWorkflowExecution)
router.post(' / trigger', requireAdmin, auditLog(AuditAction.WORKFLOW_TRIGGER, 'Workflow'), triggerWorkflow);
router.post(' / executions / : executionId / retry', requireAdmin, auditLog(AuditAction.WORKFLOW_RETRY, 'WorkflowExecution'), retryWorkflowExecution);
router.post(' / executions / : executionId / cancel', requireAdmin, auditLog(AuditAction.WORKFLOW_CANCEL, 'WorkflowExecution'), cancelWorkflowExecution)
router.get(' / stats', requireAdmin, auditLog(AuditAction.ADMIN_ACCESS, 'WorkflowStats'), getWorkflowStats)
router.get(' / available', requireAdmin, getAvailableWorkflows);
router.get(' / test - connection', requireAdmin, testN8nConnection); / / magic number
export default router;