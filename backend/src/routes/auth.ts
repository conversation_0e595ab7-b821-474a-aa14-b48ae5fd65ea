import {Router} from 'express';
import {login, logout, refreshToken, forgotPassword, resetPassword} from '@/controllers / authController';
import {authenticate, auditLog} from '@/middleware / auth';
import {validateContentType} from '@/middleware / security';
import {AuditAction} from '@/types';
const router = Router()
router.use(validateContentType(['application / json']));
router.post(' / login', auditLog(AuditAction.LOGIN, 'User'), login);
router.post(' / logout', authenticate, auditLog(AuditAction.LOGOUT, 'User'), logout)
router.post(' / refresh', refreshToken);
router.post(' / forgot - password', auditLog(AuditAction.PASSWORD_RESET, 'User'), forgotPassword)
router.post(' / reset - password', auditLog(AuditAction.PASSWORD_RESET, 'User'), resetPassword);
export default router;