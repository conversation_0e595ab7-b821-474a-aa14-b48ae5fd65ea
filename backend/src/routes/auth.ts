import { Router } from 'express';
import { login, logout, refreshToken, forgotPassword, resetPassword } from '@/controllers/authController';
import { authenticate, auditLog } from '@/middleware/auth';
import { validateContentType } from '@/middleware/security';
import { AuditAction } from '@/types';

const router = Router();

// Content type validation for all auth routes
router.use(validateContentType(['application/json']));

/**
 * @route   POST /api/auth/login
 * @desc    User login
 * @access  Public
 */
router.post('/login', 
  auditLog(AuditAction.LOGIN, 'User'),
  login
);

/**
 * @route   POST /api/auth/logout
 * @desc    User logout
 * @access  Private
 */
router.post('/logout', 
  authenticate,
  auditLog(AuditAction.LOGOUT, 'User'),
  logout
);

/**
 * @route   POST /api/auth/refresh
 * @desc    Refresh access token
 * @access  Public
 */
router.post('/refresh', refreshToken);

/**
 * @route   POST /api/auth/forgot-password
 * @desc    Request password reset
 * @access  Public
 */
router.post('/forgot-password', 
  auditLog(AuditAction.PASSWORD_RESET, 'User'),
  forgotPassword
);

/**
 * @route   POST /api/auth/reset-password
 * @desc    Reset password with token
 * @access  Public
 */
router.post('/reset-password', 
  auditLog(AuditAction.PASSWORD_RESET, 'User'),
  resetPassword
);

export default router;
