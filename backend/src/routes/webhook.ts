import {Router} from 'express';
import {verifyWebhookSignature} from '@/middleware / security';
import {handleWorkflowCompletion, handleTicketWorkflowCompletion, handleN8nHealthStatus, handleGenericWebhook} from '@/controllers / webhookController';
let router = Router();
let webhookSecret = process.env.N8N_WEBHOOK_SECRET || 'n8n - webhook - secret - key'
router.post(' / workflow - completion', verifyWebhookSignature(webhookSecret), handleWorkflowCompletion)
router.post(' / ticket - workflow', verifyWebhookSignature(webhookSecret), handleTicketWorkflowCompletion);
router.post(' / n8n - health', verifyWebhookSignature(webhookSecret), handleN8nHealthStatus);
router.post(' / generic', verifyWebhookSignature(webhookSecret), handleGenericWebhook);
export default router;