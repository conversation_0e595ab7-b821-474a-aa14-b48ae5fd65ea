import { Router } from 'express';
import { verifyWebhookSignature } from '@/middleware/security';
import {
  handleWorkflowCompletion,
  handleTicketWorkflowCompletion,
  handleN8nHealthStatus,
  handleGenericWebhook
} from '@/controllers/webhookController';

const router = Router();

const webhookSecret = process.env.N8N_WEBHOOK_SECRET || 'n8n-webhook-secret-key';

/**
 * @route   POST /api/webhook/workflow-completion
 * @desc    n8n workflow completion callback
 * @access  Webhook (with signature verification)
 */
router.post('/workflow-completion',
  verifyWebhookSignature(webhookSecret),
  handleWorkflowCompletion
);

/**
 * @route   POST /api/webhook/ticket-workflow
 * @desc    n8n ticket workflow completion callback
 * @access  Webhook (with signature verification)
 */
router.post('/ticket-workflow',
  verifyWebhookSignature(webhookSecret),
  handleTicketWorkflowCompletion
);

/**
 * @route   POST /api/webhook/n8n-health
 * @desc    n8n health status updates
 * @access  Webhook (with signature verification)
 */
router.post('/n8n-health',
  verifyWebhookSignature(webhookSecret),
  handleN8nHealthStatus
);

/**
 * @route   POST /api/webhook/generic
 * @desc    Generic n8n webhook callback
 * @access  Webhook (with signature verification)
 */
router.post('/generic',
  verifyWebhookSignature(webhookSecret),
  handleGenericWebhook
);

export default router;
