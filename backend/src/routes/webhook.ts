import { Router } from 'express';
import { verifyWebhookSignature } from '@/middleware/security';

const router = Router();

const webhookSecret = process.env.N8N_WEBHOOK_SECRET || 'n8n-webhook-secret-key';

/**
 * @route   POST /api/webhook/ticket-done
 * @desc    n8n workflow callback for ticket completion
 * @access  Webhook (with signature verification)
 */
router.post('/ticket-done', 
  verifyWebhookSignature(webhookSecret),
  (req, res) => {
    res.json({
      success: true,
      message: 'Ticket done webhook endpoint - to be implemented'
    });
  }
);

/**
 * @route   POST /api/webhook/n8n-status
 * @desc    n8n status updates
 * @access  Webhook (with signature verification)
 */
router.post('/n8n-status', 
  verifyWebhookSignature(webhookSecret),
  (req, res) => {
    res.json({
      success: true,
      message: 'n8n status webhook endpoint - to be implemented'
    });
  }
);

export default router;
