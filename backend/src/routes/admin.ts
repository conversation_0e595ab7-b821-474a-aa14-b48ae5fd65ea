import {Router} from 'express';
import {authenticate, requireAdmin, auditLog} from '@/middleware / auth';
import {tenantIsolationMiddleware} from '@/middleware / tenant'
import {AuditAction} from '@/types';
import {getTenantUsers, createTenantUser, updateTenantUser, getTenantAuditLogs, getTenantAnalytics, getTenantConfiguration, updateTenantConfiguration} from '@/controllers / adminController';
const router = Router();
router.use(authenticate);
router.use(tenantIsolationMiddleware);
router.use(requireAdmin);
router.get(' / users', auditLog(AuditAction.ADMIN_ACCESS, 'User'), getTenantUsers);
router.post(' / users', auditLog(AuditAction.USER_CREATE, 'User'), createTenantUser);
router.put(' / users /: userId', auditLog(AuditAction.USER_UPDATE, 'User'), updateTenantUser);
router.get(' / audit - logs', auditLog(AuditAction.ADMIN_ACCESS, 'AuditLog'), getTenantAuditLogs);
router.get(' / analytics', auditLog(AuditAction.ADMIN_ACCESS, 'Analytics'), getTenantAnalytics);
router.get(' / config', auditLog(AuditAction.ADMIN_ACCESS, 'TenantConfig'), getTenantConfiguration);
router.put(' / config', auditLog(AuditAction.ADMIN_ACCESS, 'TenantConfig'), updateTenantConfiguration);
export default router;