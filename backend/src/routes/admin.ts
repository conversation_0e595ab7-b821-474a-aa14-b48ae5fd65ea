import { Router } from 'express';
import { authenticate, requireAdmin, auditLog } from '@/middleware/auth';
import { tenantIsolationMiddleware } from '@/middleware/tenant';
import { AuditAction } from '@/types';
import {
  getTenantUsers,
  createTenantUser,
  updateTenantUser,
  getTenantAuditLogs,
  getTenantAnalytics,
  getTenantConfiguration,
  updateTenantConfiguration
} from '@/controllers/adminController';

const router = Router();

// All admin routes require authentication, tenant isolation, and admin role
router.use(authenticate);
router.use(tenantIsolationMiddleware);
router.use(requireAdmin);

/**
 * @route   GET /api/admin/users
 * @desc    Get all users in tenant
 * @access  Admin
 */
router.get('/users',
  auditLog(AuditAction.ADMIN_ACCESS, 'User'),
  getTenantUsers
);

/**
 * @route   POST /api/admin/users
 * @desc    Create new user in tenant
 * @access  Admin
 */
router.post('/users',
  auditLog(AuditAction.USER_CREATE, 'User'),
  createTenantUser
);

/**
 * @route   PUT /api/admin/users/:userId
 * @desc    Update user in tenant
 * @access  Admin
 */
router.put('/users/:userId',
  auditLog(AuditAction.USER_UPDATE, 'User'),
  updateTenantUser
);

/**
 * @route   GET /api/admin/audit-logs
 * @desc    Get audit logs for tenant
 * @access  Admin
 */
router.get('/audit-logs',
  auditLog(AuditAction.ADMIN_ACCESS, 'AuditLog'),
  getTenantAuditLogs
);

/**
 * @route   GET /api/admin/analytics
 * @desc    Get system analytics
 * @access  Admin
 */
router.get('/analytics',
  auditLog(AuditAction.ADMIN_ACCESS, 'Analytics'),
  getTenantAnalytics
);

/**
 * @route   GET /api/admin/config
 * @desc    Get tenant configuration
 * @access  Admin
 */
router.get('/config',
  auditLog(AuditAction.ADMIN_ACCESS, 'TenantConfig'),
  getTenantConfiguration
);

/**
 * @route   PUT /api/admin/config
 * @desc    Update tenant configuration
 * @access  Admin
 */
router.put('/config',
  auditLog(AuditAction.ADMIN_ACCESS, 'TenantConfig'),
  updateTenantConfiguration
);

export default router;
