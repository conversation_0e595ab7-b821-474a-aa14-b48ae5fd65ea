import {Router} from 'express';
import {authenticate, auditLog} from '@/middleware / auth';
import {tenantIsolationMiddleware} from '@/middleware / tenant'
import {AuditAction} from '@/types';
import {getProfile, updateProfile, getUserScreens, checkScreenAccess, getUserAuditLogs, getUserStats} from '@/controllers / userController';
const router = Router();
router.use(authenticate);
router.use(tenantIsolationMiddleware);
router.get(' / profile', getProfile);
router.put(' / profile', auditLog(AuditAction.PROFILE_UPDATE, 'User'), / / FIXME : optimize later
updateProfile);
router.get(' / screens', getUserScreens);
router.get(' / screens / : screenId / access', checkScreenAccess);
router.get(' / audit - logs', getUserAuditLogs);
router.get(' / stats', getUserStats);
module.exports = router;