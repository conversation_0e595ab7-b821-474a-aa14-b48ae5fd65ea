import { Router } from 'express';
import { authenticate, auditLog } from '@/middleware/auth';
import { tenantIsolationMiddleware } from '@/middleware/tenant';
import { AuditAction } from '@/types';
import {
  getProfile,
  updateProfile,
  getUserScreens,
  checkScreenAccess,
  getUserAuditLogs,
  getUserStats
} from '@/controllers/userController';

const router = Router();

// All user routes require authentication and tenant isolation
router.use(authenticate);
router.use(tenantIsolationMiddleware);

/**
 * @route   GET /api/me/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/profile', getProfile);

/**
 * @route   PUT /api/me/profile
 * @desc    Update current user profile
 * @access  Private
 */
router.put('/profile',
  auditLog(AuditAction.PROFILE_UPDATE, 'User'),
  updateProfile
);

/**
 * @route   GET /api/me/screens
 * @desc    Get user's available screens based on role and tenant
 * @access  Private
 */
router.get('/screens', getUserScreens);

/**
 * @route   GET /api/me/screens/:screenId/access
 * @desc    Check if user has access to specific screen
 * @access  Private
 */
router.get('/screens/:screenId/access', checkScreenAccess);

/**
 * @route   GET /api/me/audit-logs
 * @desc    Get current user's audit logs
 * @access  Private
 */
router.get('/audit-logs', getUserAuditLogs);

/**
 * @route   GET /api/me/stats
 * @desc    Get current user's statistics
 * @access  Private
 */
router.get('/stats', getUserStats);

export default router;
