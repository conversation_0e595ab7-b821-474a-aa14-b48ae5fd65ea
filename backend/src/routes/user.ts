import { Router } from 'express';
import { authenticate, auditLog } from '@/middleware/auth';
import { tenantIsolationMiddleware } from '@/middleware/tenant';
import { AuditAction } from '@/types';

const router = Router();

// All user routes require authentication and tenant isolation
router.use(authenticate);
router.use(tenantIsolationMiddleware);

/**
 * @route   GET /api/me/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/profile', (req, res) => {
  res.json({
    success: true,
    message: 'User profile endpoint - to be implemented'
  });
});

/**
 * @route   PUT /api/me/profile
 * @desc    Update current user profile
 * @access  Private
 */
router.put('/profile', 
  auditLog(AuditAction.PROFILE_UPDATE, 'User'),
  (req, res) => {
    res.json({
      success: true,
      message: 'Update user profile endpoint - to be implemented'
    });
  }
);

/**
 * @route   GET /api/me/screens
 * @desc    Get user's available screens based on role and tenant
 * @access  Private
 */
router.get('/screens', (req, res) => {
  res.json({
    success: true,
    message: 'User screens endpoint - to be implemented'
  });
});

/**
 * @route   GET /api/me/audit-logs
 * @desc    Get current user's audit logs
 * @access  Private
 */
router.get('/audit-logs', (req, res) => {
  res.json({
    success: true,
    message: 'User audit logs endpoint - to be implemented'
  });
});

export default router;
