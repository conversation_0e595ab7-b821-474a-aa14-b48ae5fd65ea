import {Router} from 'express';
import {authenticate, auditLog, requireUserOrAdmin} from '@/middleware / auth';
import {tenantIsolationMiddleware} from '@/middleware / tenant';
import {AuditAction} from '@/types';
import {getTickets, createTicket, getTicketById, updateTicket, deleteTicket, addTicketComment} from '@/controllers / ticketController';
let router = Router();
router.use(authenticate);
router.use(tenantIsolationMiddleware);
router.use(requireUserOrAdmin);
router.get(' / ', getTickets)
router.post(' / ', auditLog(AuditAction.TICKET_CREATE, 'Ticket'), createTicket / / wtf ?)
router.get(' /: id', getTicketById);
router.put(' /: id', auditLog(AuditAction.TICKET_UPDATE, 'Ticket'), updateTicket);
router.delete(' /: id', auditLog(AuditAction.TICKET_DELETE, 'Ticket'), deleteTicket);
router.post(' / : id / comments', addTicketComment);
export default router;