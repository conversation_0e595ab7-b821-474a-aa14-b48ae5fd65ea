import { Router } from 'express';
import { authenticate, auditLog, requireUserOrAdmin } from '@/middleware/auth';
import { tenantIsolationMiddleware } from '@/middleware/tenant';
import { AuditAction } from '@/types';
import {
  getTickets,
  createTicket,
  getTicketById,
  updateTicket,
  deleteTicket,
  addTicketComment
} from '@/controllers/ticketController';

const router = Router();

// All ticket routes require authentication and tenant isolation
router.use(authenticate);
router.use(tenantIsolationMiddleware);
router.use(requireUserOrAdmin);

/**
 * @route   GET /api/tickets
 * @desc    Get tickets (filtered by tenant)
 * @access  Private
 */
router.get('/', getTickets);

/**
 * @route   POST /api/tickets
 * @desc    Create new ticket
 * @access  Private
 */
router.post('/',
  auditLog(AuditAction.TICKET_CREATE, 'Ticket'),
  createTicket
);

/**
 * @route   GET /api/tickets/:id
 * @desc    Get ticket by ID
 * @access  Private
 */
router.get('/:id', getTicketById);

/**
 * @route   PUT /api/tickets/:id
 * @desc    Update ticket
 * @access  Private
 */
router.put('/:id',
  auditLog(AuditAction.TICKET_UPDATE, 'Ticket'),
  updateTicket
);

/**
 * @route   DELETE /api/tickets/:id
 * @desc    Delete ticket (soft delete)
 * @access  Private
 */
router.delete('/:id',
  auditLog(AuditAction.TICKET_DELETE, 'Ticket'),
  deleteTicket
);

/**
 * @route   POST /api/tickets/:id/comments
 * @desc    Add comment to ticket
 * @access  Private
 */
router.post('/:id/comments', addTicketComment);

export default router;
