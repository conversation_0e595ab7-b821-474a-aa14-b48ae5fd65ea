{"name": "flowbit-backend", "version": "1.0.0", "description": "Multi-tenant workflow system backend API", "main": "dist/app.js", "scripts": {"start": "node dist/app.js", "dev": "nodemon src/app.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --config jest.integration.config.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "typecheck": "tsc --noEmit", "seed": "node scripts/seed-database.js", "migrate": "node scripts/migrate-database.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "redis": "^4.6.12", "jsonwebtoken": "^9.0.2", "bcrypt": "^5.1.1", "helmet": "^7.1.0", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "joi": "^17.11.0", "socket.io": "^4.7.4", "axios": "^1.6.2", "nodemailer": "^6.9.7", "winston": "^3.11.0", "compression": "^1.7.4", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "uuid": "^9.0.1", "crypto": "^1.0.1", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.5", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/express-session": "^1.17.10", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node": "^10.9.2", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "prettier": "^3.1.1", "mongodb-memory-server": "^9.1.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["multi-tenant", "workflow", "n8n", "saas", "typescript", "express", "mongodb"], "author": "Flowbit Team", "license": "MIT"}